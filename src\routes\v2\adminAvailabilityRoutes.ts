import express from 'express';
import { adminAvailabilityController } from '../../controllers/adminAvailabilityController';
import { authenticate, authorize } from '../../middleware/auth';

const router = express.Router();

// Public routes (no authentication required)
router.get('/time-slots/:date', adminAvailabilityController.getAdminTimeSlots);
router.get('/check', adminAvailabilityController.checkTimeSlotAvailability);

// Protected routes (authentication required)
router.get('/settings', authenticate, authorize('admin'), adminAvailabilityController.getGlobalSettings);
router.put('/settings', authenticate, authorize('admin'), adminAvailabilityController.updateGlobalSettings);

// Get availability for a date range
router.get('/', authenticate, authorize('admin'), adminAvailabilityController.getAvailability);

// Set availability for a specific date
router.post('/', authenticate, authorize('admin'), adminAvailabilityController.setAvailability);

// Set availability for multiple dates (bulk operation)
router.post('/bulk', authenticate, authorize('admin'), adminAvailabilityController.setBulkAvailability);

// Delete availability for a specific date
router.delete('/:date', authenticate, authorize('admin'), adminAvailabilityController.deleteAvailability);

// Delete availability for multiple dates (bulk operation)
router.delete('/', authenticate, authorize('admin'), adminAvailabilityController.deleteBulkAvailability);

export default router;
