import { API_CONFIG } from './config';

const API_BASE_URL = API_CONFIG.BASE_URL;

// Utility function to convert 12-hour time to 24-hour format
function convertTo24Hour(time12h: string): string {
  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');

  if (hours === '12') {
    hours = '00';
  }

  if (modifier === 'PM') {
    hours = (parseInt(hours, 10) + 12).toString();
  }

  return `${hours.padStart(2, '0')}:${minutes}`;
}

export interface CreateAppointmentRequest {
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  date: string;
  time: string;
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    paymentMethod: 'cashapp' | 'zelle';
    paymentDetails: string;
  };
  totalPrice: number;
}

export interface Appointment {
  id: string;
  userId?: string;
  serviceId: string;
  serviceName: string;
  servicePrice: number;
  addOns: Array<{
    id: string;
    name: string;
    price: number;
    duration: number;
  }>;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    paymentMethod: 'cashapp' | 'zelle';
    paymentDetails: string;
  };
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export const appointmentAPI = {
  // Create a new appointment (public endpoint)
  async createAppointment(appointmentData: CreateAppointmentRequest): Promise<Appointment> {
    // Check for payment proof data in localStorage (required)
    const paymentProofData = localStorage.getItem('paymentProofData');
    let paymentProof = null;

    if (paymentProofData) {
      try {
        paymentProof = JSON.parse(paymentProofData);
        console.log('Found payment proof data in localStorage:', paymentProof);
      } catch (error) {
        console.error('Error parsing payment proof data from localStorage:', error);
      }
    }

    // Payment proof is required for appointment creation
    if (!paymentProof || !paymentProof.url) {
      throw new Error('Payment proof is required to create an appointment. Please upload payment proof first.');
    }

    // Transform data to match backend expectations
    const backendData = {
      serviceId: appointmentData.serviceId,
      date: appointmentData.date,
      time: convertTo24Hour(appointmentData.time), // Convert 12-hour to 24-hour format
      customerInfo: {
        firstName: appointmentData.customerInfo.firstName,
        lastName: appointmentData.customerInfo.lastName,
        email: appointmentData.customerInfo.email,
        phone: appointmentData.customerInfo.phone ? `+1${appointmentData.customerInfo.phone.replace(/\D/g, '')}` : undefined // Format phone with +1 prefix
      },
      addOns: appointmentData.addOns || [],
      userId: undefined, // Will be set if user is logged in
      notes: '', // Add notes field if needed
      paymentProof: paymentProof // Include payment proof (required)
    };

    console.log('Sending appointment data:', backendData);

    const response = await fetch(`${API_BASE_URL}/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // Removed authorization header - endpoint is now public
      },
      body: JSON.stringify(backendData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create appointment');
    }

    const result = await response.json();

    // Clear payment proof data from localStorage after successful appointment creation
    if (paymentProof) {
      localStorage.removeItem('paymentProofData');
      localStorage.removeItem('paymentProofUrl');
      console.log('Cleared payment proof data from localStorage after successful appointment creation');
    }

    return result;
  },

  // Get all appointments (admin only)
  async getAppointments(): Promise<Appointment[]> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch appointments');
    }

    const result = await response.json();

    console.log(result)

    // Handle different response formats
    if (result && result.success && Array.isArray(result.data)) {
      return result.data;


    } else if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.appointments)) {
      return result.appointments;

    } else {
      console.warn('Unexpected response format:', result);
      return [];
    }
  },

  // Get user's appointments
  async getUserAppointments(): Promise<Appointment[]> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/user`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user appointments');
    }

    const result = await response.json();

    // Handle different response formats
    if (result && result.success && Array.isArray(result.data)) {
      return result.data;
    } else if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.appointments)) {
      return result.appointments;
    } else {
      console.warn('Unexpected response format:', result);
      return [];
    }
  },

  // Update appointment status (admin only)
  async updateAppointmentStatus(appointmentId: string, status: Appointment['status']): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ status })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update appointment status');
    }

    return response.json();
  },

  // Get appointment by ID
  async getAppointment(appointmentId: string): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch appointment');
    }

    return response.json();
  },

  // Cancel appointment
  async cancelAppointment(appointmentId: string): Promise<Appointment> {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    const response = await fetch(`${API_BASE_URL}/appointments/${appointmentId}/cancel`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel appointment');
    }

    return response.json();
  },

  // Check if a specific time slot is available
  async checkAvailability(date: string, time: string): Promise<{ isAvailable: boolean; reason?: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/availability/check?date=${date}&time=${encodeURIComponent(time)}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to check availability');
    }

    const result = await response.json();
    return result.data;
  },

  // Get available time slots for a date
  async getAvailableTimeSlots(date: string): Promise<string[]> {
    const response = await fetch(`${API_BASE_URL}/admin/availability/time-slots/${date}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch available time slots');
    }

    const result = await response.json();

    // Handle the admin API response format
    if (result && result.success && result.data && Array.isArray(result.data.timeSlots)) {
      // Filter and extract available time slots
      return result.data.timeSlots
        .filter((slot: any) => {
          // Handle both formats: objects with isAvailable property or just strings
          if (typeof slot === 'string') return true; // String format means it's available
          return slot.isAvailable === true; // Object format, check isAvailable
        })
        .map((slot: any) => {
          // Extract time from both formats
          return typeof slot === 'string' ? slot : slot.time;
        });
    } else {
      console.warn('Unexpected response format for time slots:', result);
      return [];
    }
  }
};
