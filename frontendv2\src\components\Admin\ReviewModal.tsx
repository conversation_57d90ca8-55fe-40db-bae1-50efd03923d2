import { useState, useEffect } from 'react';
import Modal from '../Modal';

interface Review {
  _id: string;
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  service?: {
    _id: string;
    name: string;
  };
  appointment?: {
    _id: string;
    date: string;
    time: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  adminResponse?: string;
  adminResponseDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  review?: Review | null;
  mode: 'view' | 'edit' | 'respond';
}

export default function ReviewModal({
  isOpen,
  onClose,
  onSave,
  review,
  mode
}: ReviewModalProps) {
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    comment: '',
    adminResponse: '',
    status: 'pending' as 'pending' | 'approved' | 'rejected'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  // Initialize form data when review changes
  useEffect(() => {
    if (review) {
      setFormData({
        rating: review.rating,
        title: review.title || '',
        comment: review.comment || '',
        adminResponse: review.adminResponse || '',
        status: review.status
      });
    } else {
      setFormData({
        rating: 5,
        title: '',
        comment: '',
        adminResponse: '',
        status: 'pending'
      });
    }
    setErrors({});
  }, [review]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (mode === 'edit') {
      if (formData.rating < 1 || formData.rating > 5) {
        newErrors.rating = 'Rating must be between 1 and 5';
      }
    }

    if (mode === 'respond') {
      if (!formData.adminResponse.trim()) {
        newErrors.adminResponse = 'Response is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (mode === 'view') {
      onClose();
      return;
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const submitData: any = {};

      if (mode === 'edit') {
        submitData.rating = formData.rating;
        submitData.title = formData.title.trim();
        submitData.comment = formData.comment.trim();
      } else if (mode === 'respond') {
        submitData.adminResponse = formData.adminResponse.trim();
      }

      await onSave(submitData);
    } catch (error) {
      console.error('Error saving review:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get modal title
  const getModalTitle = () => {
    switch (mode) {
      case 'view': return 'Review Details';
      case 'edit': return 'Edit Review';
      case 'respond': return 'Add Business Response';
      default: return 'Review';
    }
  };

  // Render stars for display
  const renderStars = (rating: number) => {
    return (
      <div className="rating-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${star <= rating ? 'filled' : 'empty'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  // Render interactive stars for editing
  const renderInteractiveStars = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="interactive-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`interactive-star ${star <= rating ? 'filled' : ''}`}
            onClick={() => onRatingChange(star)}
          >
            ★
          </button>
        ))}
        <span className="rating-label">
          {rating} out of 5 stars
        </span>
      </div>
    );
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'status-badge approved';
      case 'rejected':
        return 'status-badge rejected';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  if (!review) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={getModalTitle()}>
      <form onSubmit={handleSubmit} className="review-modal-form">
        {/* Review Information */}
        {review && (
          <div className="review-info-section">
            <div className="customer-info">
              <h4>
                {review.customerName || 
                 (review.user ? `${review.user.firstName} ${review.user.lastName}` : 'Anonymous')}
              </h4>
              {review.isVerifiedPurchase && (
                <span className="verified-badge">✓ Verified Purchase</span>
              )}
            </div>

            <div className="service-info">
              <strong>Service:</strong> {review.service?.name || 'Unknown Service'}
              {review.appointment && (
                <div className="appointment-info">
                  <strong>Appointment:</strong> {new Date(review.appointment.date).toLocaleDateString()} at {review.appointment.time}
                </div>
              )}
            </div>

            <div className="review-meta">
              <div className="status-info">
                <strong>Status:</strong>
                <span className={getStatusBadgeClass(review.status)}>
                  {review.status.toUpperCase()}
                </span>
              </div>
              <div className="dates-info">
                <div><strong>Created:</strong> {new Date(review.createdAt).toLocaleDateString()}</div>
                <div><strong>Updated:</strong> {new Date(review.updatedAt).toLocaleDateString()}</div>
              </div>
            </div>
          </div>
        )}

        {/* Rating */}
        <div className="form-group">
          <label className="form-label">Rating</label>
          {mode === 'edit' ? (
            <>
              {renderInteractiveStars(formData.rating, (rating) => 
                handleInputChange('rating', rating)
              )}
              {errors.rating && (
                <div className="error-message">{errors.rating}</div>
              )}
            </>
          ) : (
            renderStars(review?.rating || 0)
          )}
        </div>

        {/* Title */}
        <div className="form-group">
          <label className="form-label">Title</label>
          {mode === 'edit' ? (
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="form-input"
              placeholder="Review title..."
              maxLength={100}
            />
          ) : (
            <div className="form-display">
              {review?.title || 'No title'}
            </div>
          )}
        </div>

        {/* Comment */}
        <div className="form-group">
          <label className="form-label">Review Comment</label>
          {mode === 'edit' ? (
            <textarea
              value={formData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              rows={4}
              className="form-input form-textarea"
              placeholder="Review comment..."
              maxLength={1000}
            />
          ) : (
            <div className="form-display">
              {review?.comment || 'No comment'}
            </div>
          )}
        </div>

        {/* Admin Response */}
        <div className="form-group">
          <label className="form-label">Business Response</label>
          {mode === 'respond' ? (
            <>
              <textarea
                value={formData.adminResponse}
                onChange={(e) => handleInputChange('adminResponse', e.target.value)}
                rows={4}
                className="form-input form-textarea"
                placeholder="Write your response to this review..."
                maxLength={500}
                required
              />
              {errors.adminResponse && (
                <div className="error-message">{errors.adminResponse}</div>
              )}
            </>
          ) : (
            <div className="form-display">
              {review?.adminResponse ? (
                <div className="admin-response">
                  <div className="admin-response-text">{review.adminResponse}</div>
                  {review.adminResponseDate && (
                    <div className="admin-response-date">
                      Responded on: {new Date(review.adminResponseDate).toLocaleDateString()}
                    </div>
                  )}
                </div>
              ) : (
                'No response yet'
              )}
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          {mode !== 'view' && (
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? 'Saving...' : mode === 'edit' ? 'Update Review' : 'Add Response'}
            </button>
          )}
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline"
          >
            {mode === 'view' ? 'Close' : 'Cancel'}
          </button>
        </div>
      </form>
    </Modal>
  );
}
