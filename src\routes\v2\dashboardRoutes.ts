import { Router } from 'express';
import { Request, Response } from 'express';
import { Appointment, Service, User, Review } from '../../models';
import { authenticate } from '../../middleware/auth';
import { sendSuccess, sendError } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// GET /api/v2/dashboard - Get user dashboard data (authenticated)
router.get('/', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const user = req.user;

    // Get all user appointments with full population
    const appointments = await Appointment.find({ user: user._id })
      .populate('service', 'name duration price category description')
      .populate('user', 'name email phone')
      .sort({ date: -1, time: -1 });

    // Get appointment statistics
    const appointmentStats = {
      total: appointments.length,
      pending: appointments.filter(apt => apt.status === 'pending').length,
      confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
      completed: appointments.filter(apt => apt.status === 'completed').length,
      cancelled: appointments.filter(apt => apt.status === 'cancelled').length
    };

    // Get user's reviews
    const userReviews = await Review.find({ user: user._id })
      .populate('service', 'name category')
      .populate({
        path: 'appointment',
        select: 'date time service',
        populate: {
          path: 'service',
          select: 'name category'
        }
      })
      .sort({ createdAt: -1 })
      .limit(10);

    // Get completed appointments that can be reviewed (not already reviewed)
    const reviewedAppointmentIds = userReviews
      .filter(review => review.appointment)
      .map(review => (review.appointment as any)._id.toString());

    const completedAppointments = appointments
      .filter(apt => apt.status === 'completed' && !reviewedAppointmentIds.includes(apt._id.toString()))
      .slice(0, 5);

    // Format user reviews
    const formattedReviews = userReviews.map(review => {
      // Get service data from either direct service field or appointment.service
      const serviceData = (review.service as any) || (review.appointment as any)?.service;

      return {
        id: review._id,
        appointmentId: (review.appointment as any)?._id,
        serviceId: serviceData?._id,
        serviceName: serviceData?.name,
        serviceCategory: serviceData?.category,
        appointmentDate: (review.appointment as any)?.date,
        appointmentTime: (review.appointment as any)?.time,
        rating: review.rating,
        title: review.title,
        comment: review.comment,
        status: review.status,
        isVerifiedPurchase: review.isVerifiedPurchase,
        adminResponse: review.adminResponse,
        adminResponseDate: review.adminResponseDate,
        createdAt: review.createdAt,
        updatedAt: review.updatedAt
      };
    });

    // Format completed appointments available for review
    const formattedCompletedAppointments = completedAppointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      serviceCategory: (appointment.service as any).category,
      servicePrice: (appointment.service as any).price,
      serviceDuration: (appointment.service as any).duration,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    // Get recent appointments (last 5)
    const recentAppointments = appointments.slice(0, 5).map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)?._id,
      serviceName: (appointment.service as any)?.name,
      servicePrice: (appointment.service as any)?.price,
      serviceDuration: (appointment.service as any)?.duration,
      serviceCategory: (appointment.service as any)?.category,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      notes: appointment.message,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt
    }));

    // Get upcoming appointments
    const now = new Date();
    const upcomingAppointments = appointments
      .filter(apt => {
        const appointmentDate = new Date(apt.date);
        return appointmentDate >= now && (apt.status === 'pending' || apt.status === 'confirmed');
      })
      .slice(0, 3)
      .map(appointment => ({
        id: appointment._id,
        serviceId: (appointment.service as any)?._id,
        serviceName: (appointment.service as any)?.name,
        servicePrice: (appointment.service as any)?.price,
        serviceDuration: (appointment.service as any)?.duration,
        date: appointment.date,
        time: appointment.time,
        status: appointment.status,
        notes: appointment.message
      }));

    // Calculate total spent
    const totalSpent = appointments
      .filter(apt => apt.status === 'completed')
      .reduce((sum, apt) => {
        const servicePrice = (apt.service as any)?.price || 0;
        return sum + servicePrice;
      }, 0);

    // Get favorite services (most booked)
    const serviceBookings = appointments.reduce((acc: any, apt) => {
      const serviceId = (apt.service as any)?._id?.toString();
      const serviceName = (apt.service as any)?.name;
      if (serviceId && serviceName) {
        if (!acc[serviceId]) {
          acc[serviceId] = {
            id: serviceId,
            name: serviceName,
            count: 0,
            price: (apt.service as any)?.price || 0,
            category: (apt.service as any)?.category || 'General'
          };
        }
        acc[serviceId].count++;
      }
      return acc;
    }, {});

    const favoriteServices = Object.values(serviceBookings)
      .sort((a: any, b: any) => b.count - a.count)
      .slice(0, 3);

    // Remove password from user object
    const userResponse = user.toObject();
    const { password, ...userWithoutPassword } = userResponse;

    // Send comprehensive dashboard data
    sendSuccess(res, 'User dashboard data retrieved successfully', {
      user: userWithoutPassword,
      appointments: {
        all: appointments.map(appointment => ({
          id: appointment._id,
          serviceId: (appointment.service as any)?._id,
          serviceName: (appointment.service as any)?.name,
          servicePrice: (appointment.service as any)?.price,
          serviceDuration: (appointment.service as any)?.duration,
          serviceCategory: (appointment.service as any)?.category,
          date: appointment.date,
          time: appointment.time,
          status: appointment.status,
          customerInfo: appointment.customerInfo,
          notes: appointment.message,
          createdAt: appointment.createdAt,
          updatedAt: appointment.updatedAt
        })),
        recent: recentAppointments,
        upcoming: upcomingAppointments
      },
      userReviews: formattedReviews,
      completedAppointmentsToReview: formattedCompletedAppointments,
      statistics: {
        appointments: appointmentStats,
        totalSpent,
        favoriteServices,
        memberSince: user.createdAt,
        lastActivity: user.updatedAt,
        reviews: {
          total: userReviews.length,
          averageRating: userReviews.length > 0
            ? userReviews.reduce((sum, review) => sum + review.rating, 0) / userReviews.length
            : 0,
          pendingReviews: formattedCompletedAppointments.length
        }
      }
    });
  } catch (error) {
    console.error('Get user dashboard data error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/dashboard/user - Get user dashboard data by email/userId (public endpoint for compatibility)
router.get('/user', async (req: Request, res: Response) => {
  try {
    const { email, userId } = req.query;

    if (!email && !userId) {
      sendError(res, 'Email or userId query parameter is required. Usage: ?email=<EMAIL> or ?userId=123', undefined, 400);
      return;
    }

    // Find user by email or userId
    let user = null;
    if (userId) {
      user = await User.findById(userId);
    } else if (email) {
      user = await User.findOne({ email: email.toString().toLowerCase() });
    }

    if (!user) {
      sendError(res, 'User not found', undefined, 404);
      return;
    }

    // Get all user appointments with full population
    const appointments = await Appointment.find({ user: user._id })
      .populate('service', 'name duration price category description')
      .populate('user', 'name email phone')
      .sort({ date: -1, time: -1 });

    // Get appointment statistics
    const appointmentStats = {
      total: appointments.length,
      pending: appointments.filter(apt => apt.status === 'pending').length,
      confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
      completed: appointments.filter(apt => apt.status === 'completed').length,
      cancelled: appointments.filter(apt => apt.status === 'cancelled').length
    };

    // Get user's reviews
    const userReviews = await Review.find({ user: user._id })
      .populate('service', 'name category')
      .populate({
        path: 'appointment',
        select: 'date time service',
        populate: {
          path: 'service',
          select: 'name category'
        }
      })
      .sort({ createdAt: -1 })
      .limit(10);

    // Get completed appointments that can be reviewed (not already reviewed)
    const reviewedAppointmentIds = userReviews
      .filter(review => review.appointment)
      .map(review => (review.appointment as any)._id.toString());

    const completedAppointments = appointments
      .filter(apt => apt.status === 'completed' && !reviewedAppointmentIds.includes(apt._id.toString()))
      .slice(0, 5);

    // Format user reviews
    const formattedReviews = userReviews.map(review => {
      // Get service data from either direct service field or appointment.service
      const serviceData = (review.service as any) || (review.appointment as any)?.service;

      return {
        id: review._id,
        appointmentId: (review.appointment as any)?._id,
        serviceId: serviceData?._id,
        serviceName: serviceData?.name,
        serviceCategory: serviceData?.category,
        appointmentDate: (review.appointment as any)?.date,
        appointmentTime: (review.appointment as any)?.time,
        rating: review.rating,
        title: review.title,
        comment: review.comment,
        status: review.status,
        isVerifiedPurchase: review.isVerifiedPurchase,
        adminResponse: review.adminResponse,
        adminResponseDate: review.adminResponseDate,
        createdAt: review.createdAt,
        updatedAt: review.updatedAt
      };
    });

    // Format completed appointments available for review
    const formattedCompletedAppointments = completedAppointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      serviceCategory: (appointment.service as any).category,
      servicePrice: (appointment.service as any).price,
      serviceDuration: (appointment.service as any).duration,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    // Get recent appointments (last 5)
    const recentAppointments = appointments.slice(0, 5).map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)?._id,
      serviceName: (appointment.service as any)?.name,
      servicePrice: (appointment.service as any)?.price,
      serviceDuration: (appointment.service as any)?.duration,
      serviceCategory: (appointment.service as any)?.category,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      notes: appointment.message,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt
    }));

    // Get upcoming appointments
    const now = new Date();
    const upcomingAppointments = appointments
      .filter(apt => {
        const appointmentDate = new Date(apt.date);
        return appointmentDate >= now && (apt.status === 'pending' || apt.status === 'confirmed');
      })
      .slice(0, 3)
      .map(appointment => ({
        id: appointment._id,
        serviceId: (appointment.service as any)?._id,
        serviceName: (appointment.service as any)?.name,
        servicePrice: (appointment.service as any)?.price,
        serviceDuration: (appointment.service as any)?.duration,
        date: appointment.date,
        time: appointment.time,
        status: appointment.status,
        notes: appointment.message
      }));

    // Calculate total spent
    const totalSpent = appointments
      .filter(apt => apt.status === 'completed')
      .reduce((sum, apt) => {
        const servicePrice = (apt.service as any)?.price || 0;
        return sum + servicePrice;
      }, 0);

    // Get favorite services (most booked)
    const serviceBookings = appointments.reduce((acc: any, apt) => {
      const serviceId = (apt.service as any)?._id?.toString();
      const serviceName = (apt.service as any)?.name;
      if (serviceId && serviceName) {
        if (!acc[serviceId]) {
          acc[serviceId] = {
            id: serviceId,
            name: serviceName,
            count: 0,
            price: (apt.service as any)?.price || 0,
            category: (apt.service as any)?.category || 'General'
          };
        }
        acc[serviceId].count++;
      }
      return acc;
    }, {});

    const favoriteServices = Object.values(serviceBookings)
      .sort((a: any, b: any) => b.count - a.count)
      .slice(0, 3);

    // Remove password from user object
    const userResponse = user.toObject();
    const { password, ...userWithoutPassword } = userResponse;

    // Send comprehensive dashboard data
    sendSuccess(res, 'User dashboard data retrieved successfully', {
      user: userWithoutPassword,
      appointments: {
        all: appointments.map(appointment => ({
          id: appointment._id,
          serviceId: (appointment.service as any)?._id,
          serviceName: (appointment.service as any)?.name,
          servicePrice: (appointment.service as any)?.price,
          serviceDuration: (appointment.service as any)?.duration,
          serviceCategory: (appointment.service as any)?.category,
          date: appointment.date,
          time: appointment.time,
          status: appointment.status,
          customerInfo: appointment.customerInfo,
          notes: appointment.message,
          createdAt: appointment.createdAt,
          updatedAt: appointment.updatedAt
        })),
        recent: recentAppointments,
        upcoming: upcomingAppointments
      },
      userReviews: formattedReviews,
      completedAppointmentsToReview: formattedCompletedAppointments,
      statistics: {
        appointments: appointmentStats,
        totalSpent,
        favoriteServices,
        memberSince: user.createdAt,
        lastActivity: user.updatedAt,
        reviews: {
          total: userReviews.length,
          averageRating: userReviews.length > 0
            ? userReviews.reduce((sum, review) => sum + review.rating, 0) / userReviews.length
            : 0,
          pendingReviews: formattedCompletedAppointments.length
        }
      }
    });
  } catch (error) {
    console.error('Get user dashboard data error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
