"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const response_1 = require("../../utils/response");
const adminAvailabilityRoutes_1 = __importDefault(require("./adminAvailabilityRoutes"));
const emailService_1 = require("../../services/emailService");
const router = (0, express_1.Router)();
// Mount availability routes first (some routes are public)
router.use('/availability', adminAvailabilityRoutes_1.default);
// All other admin routes require authentication and admin role
router.use(auth_1.authenticate);
router.use((0, auth_1.authorize)('admin'));
// GET /api/v2/admin/dashboard - Get admin dashboard overview
router.get('/dashboard', async (req, res) => {
    try {
        // Get counts for dashboard overview
        const totalUsers = await models_1.User.countDocuments({ role: 'user' });
        const totalAppointments = await models_1.Appointment.countDocuments();
        const totalServices = await models_1.Service.countDocuments({ isActive: true });
        const pendingPayments = await models_1.PaymentConfirmation.countDocuments({ status: 'pending' });
        // Get recent appointments
        const recentAppointments = await models_1.Appointment.find()
            .populate('user', 'firstName lastName email')
            .populate('service', 'name price')
            .sort({ createdAt: -1 })
            .limit(10);
        // Get pending payment confirmations
        const pendingPaymentConfirmations = await models_1.PaymentConfirmation.find({ status: 'pending' })
            .populate('user', 'firstName lastName email')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .limit(10);
        // Get appointment statistics by status
        const appointmentStats = await models_1.Appointment.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get payment confirmation statistics
        const paymentStats = await models_1.PaymentConfirmation.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$amount' }
                }
            }
        ]);
        // Format recent appointments with robust null checks
        const formattedAppointments = recentAppointments
            .filter(appointment => {
            // Check if appointment has valid user and service references
            return appointment &&
                appointment.user &&
                appointment.service &&
                appointment.user._id &&
                appointment.service._id;
        })
            .map(appointment => ({
            id: appointment._id,
            user: {
                id: appointment.user._id,
                name: `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim(),
                email: appointment.user.email || ''
            },
            service: {
                id: appointment.service._id,
                name: appointment.service.name || '',
                price: appointment.service.price || 0
            },
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            totalAmount: appointment.service.price || 0,
            paymentProofs: appointment.paymentProofs || [],
            paymentStatus: appointment.paymentStatus || 'pending',
            createdAt: appointment.createdAt
        }));
        // Format pending payment confirmations with robust null checks
        const formattedPaymentConfirmations = pendingPaymentConfirmations
            .filter(confirmation => {
            // Check if confirmation has valid user reference
            return confirmation &&
                confirmation.user &&
                confirmation.user._id;
        })
            .map(confirmation => ({
            id: confirmation._id,
            user: {
                id: confirmation.user._id,
                name: `${confirmation.user.firstName || ''} ${confirmation.user.lastName || ''}`.trim(),
                email: confirmation.user.email || ''
            },
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name || '',
            orderId: confirmation.order?._id,
            amount: confirmation.amount || 0,
            paymentMethod: confirmation.paymentMethod || '',
            proofImage: confirmation.proofImage || '',
            createdAt: confirmation.createdAt
        }));
        const dashboardData = {
            overview: {
                totalUsers,
                totalAppointments,
                totalServices,
                pendingPayments
            },
            statistics: {
                appointments: appointmentStats.reduce((acc, stat) => {
                    acc[stat._id] = stat.count;
                    return acc;
                }, {}),
                payments: paymentStats.reduce((acc, stat) => {
                    acc[stat._id] = {
                        count: stat.count,
                        totalAmount: stat.totalAmount
                    };
                    return acc;
                }, {})
            },
            recentAppointments: formattedAppointments,
            pendingPaymentConfirmations: formattedPaymentConfirmations
        };
        (0, response_1.sendSuccess)(res, 'Admin dashboard data retrieved successfully', dashboardData);
    }
    catch (error) {
        console.error('Get admin dashboard error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/appointments - Get all appointments with filtering and pagination
router.get('/appointments', async (req, res) => {
    try {
        const { page = 1, limit = 20, status, date, search, sortBy = 'date', sortOrder = 'desc' } = req.query;
        const filter = {};
        // Status filter
        if (status && status !== 'all') {
            filter.status = status;
        }
        // Date filter
        if (date) {
            const searchDate = new Date(date);
            const nextDay = new Date(searchDate);
            nextDay.setDate(nextDay.getDate() + 1);
            filter.date = {
                $gte: searchDate,
                $lt: nextDay
            };
        }
        // Search filter
        if (search) {
            filter.$or = [
                { 'customerInfo.name': { $regex: search, $options: 'i' } },
                { 'customerInfo.email': { $regex: search, $options: 'i' } },
                { 'customerInfo.phone': { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const appointments = await models_1.Appointment.find(filter)
            .populate('user', 'firstName lastName email phone')
            .populate('service', 'name price duration category')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await models_1.Appointment.countDocuments(filter);
        const responseData = {
            appointments: appointments.map(apt => {
                return {
                    id: apt._id,
                    user: apt.user ? {
                        id: apt.user._id,
                        name: `${apt.user.firstName || ''} ${apt.user.lastName || ''}`.trim(),
                        email: apt.user.email,
                        phone: apt.user.phone
                    } : null,
                    customerInfo: apt.customerInfo,
                    service: apt.service ? {
                        id: apt.service._id,
                        name: apt.service.name,
                        price: apt.service.price,
                        duration: apt.service.duration,
                        category: apt.service.category
                    } : null,
                    date: apt.date,
                    time: apt.time,
                    status: apt.status,
                    paymentStatus: apt.paymentStatus,
                    message: apt.message,
                    paymentProofs: apt.paymentProofs || [],
                    createdAt: apt.createdAt,
                    updatedAt: apt.updatedAt
                };
            }),
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        };
        (0, response_1.sendSuccess)(res, 'Appointments retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/appointments/:id - Get single appointment details
router.get('/appointments/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const appointment = await models_1.Appointment.findById(id)
            .populate('user', 'firstName lastName email phone')
            .populate('service', 'name price duration category description');
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        const responseData = {
            id: appointment._id,
            user: appointment.user ? {
                id: appointment.user._id,
                name: `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim(),
                email: appointment.user.email,
                phone: appointment.user.phone
            } : null,
            customerInfo: appointment.customerInfo,
            service: appointment.service ? {
                id: appointment.service._id,
                name: appointment.service.name,
                price: appointment.service.price,
                duration: appointment.service.duration,
                category: appointment.service.category,
                description: appointment.service.description
            } : null,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            message: appointment.message,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Appointment details retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get appointment details error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/appointments/:id - Update appointment
router.put('/appointments/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { date, time, customerInfo, message, status } = req.body;
        const appointment = await models_1.Appointment.findById(id);
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        // Update fields if provided
        if (date)
            appointment.date = new Date(date);
        if (time)
            appointment.time = time;
        if (customerInfo)
            appointment.customerInfo = { ...appointment.customerInfo, ...customerInfo };
        if (message !== undefined)
            appointment.message = message;
        if (status)
            appointment.status = status;
        await appointment.save();
        // Populate for response
        await appointment.populate('user', 'firstName lastName email phone');
        await appointment.populate('service', 'name price duration category');
        const responseData = {
            id: appointment._id,
            user: appointment.user ? {
                id: appointment.user._id,
                name: `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim(),
                email: appointment.user.email,
                phone: appointment.user.phone
            } : null,
            customerInfo: appointment.customerInfo,
            service: appointment.service ? {
                id: appointment.service._id,
                name: appointment.service.name,
                price: appointment.service.price,
                duration: appointment.service.duration,
                category: appointment.service.category
            } : null,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            message: appointment.message,
            updatedAt: appointment.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Appointment updated successfully', responseData);
    }
    catch (error) {
        console.error('Update appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/appointments/:id/status - Update appointment status
router.put('/appointments/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, sendNotification = true } = req.body;
        if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status', undefined, 400);
            return;
        }
        const appointment = await models_1.Appointment.findById(id)
            .populate('user', 'firstName lastName email')
            .populate('service', 'name price');
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        const previousStatus = appointment.status;
        appointment.status = status;
        await appointment.save();
        // Send email notification if status changed and notification is enabled
        if (sendNotification && previousStatus !== status) {
            try {
                const emailService = new emailService_1.EmailService();
                const userInfo = {
                    name: appointment.user ?
                        `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim() :
                        appointment.customerInfo.name,
                    email: appointment.user ?
                        appointment.user.email :
                        appointment.customerInfo.email,
                    firstName: appointment.user ?
                        appointment.user.firstName :
                        appointment.customerInfo.name.split(' ')[0],
                    lastName: appointment.user ?
                        appointment.user.lastName :
                        appointment.customerInfo.name.split(' ').slice(1).join(' ')
                };
                const appointmentInfo = {
                    _id: appointment._id,
                    service: appointment.service.name,
                    date: appointment.date,
                    time: appointment.time,
                    status: appointment.status
                };
                // Send appropriate email based on status
                switch (status) {
                    case 'confirmed':
                        await emailService.sendAppointmentConfirmation(userInfo, appointmentInfo);
                        break;
                    case 'cancelled':
                        await emailService.sendAppointmentCancellation(userInfo, appointmentInfo);
                        break;
                    case 'completed':
                        // Could send a thank you email or request for review
                        break;
                }
            }
            catch (emailError) {
                console.error('Failed to send email notification:', emailError);
                // Don't fail the request if email fails
            }
        }
        const responseData = {
            id: appointment._id,
            status: appointment.status,
            user: appointment.user ? {
                name: `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim(),
                email: appointment.user.email
            } : appointment.customerInfo,
            service: {
                name: appointment.service.name
            },
            date: appointment.date,
            time: appointment.time,
            updatedAt: appointment.updatedAt,
            emailSent: sendNotification && previousStatus !== status
        };
        (0, response_1.sendSuccess)(res, 'Appointment status updated successfully', responseData);
    }
    catch (error) {
        console.error('Update appointment status error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/admin/appointments/:id - Delete appointment
router.delete('/appointments/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const appointment = await models_1.Appointment.findById(id);
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        await models_1.Appointment.findByIdAndDelete(id);
        (0, response_1.sendSuccess)(res, 'Appointment deleted successfully', { id });
    }
    catch (error) {
        console.error('Delete appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// POST /api/v2/admin/appointments - Create new appointment
router.post('/appointments', async (req, res) => {
    try {
        const { userId, service, date, time, customerInfo, message, sendConfirmation = true } = req.body;
        // Check if time slot is available
        const existingAppointment = await models_1.Appointment.findOne({
            date: new Date(date),
            time,
            status: { $in: ['pending', 'confirmed'] }
        });
        if (existingAppointment) {
            (0, response_1.sendError)(res, 'Time slot is already booked', undefined, 400);
            return;
        }
        const appointmentData = {
            service,
            date: new Date(date),
            time,
            customerInfo,
            message,
            status: 'pending'
        };
        if (userId && userId !== 'temp-user') {
            appointmentData.user = userId;
        }
        const appointment = new models_1.Appointment(appointmentData);
        await appointment.save();
        // Populate for response
        await appointment.populate('user', 'firstName lastName email phone');
        await appointment.populate('service', 'name price duration category');
        // Send confirmation email if requested
        if (sendConfirmation) {
            try {
                const emailService = new emailService_1.EmailService();
                const userInfo = {
                    name: appointment.user ?
                        `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim() :
                        appointment.customerInfo.name,
                    email: appointment.user ?
                        appointment.user.email :
                        appointment.customerInfo.email,
                    firstName: appointment.user ?
                        appointment.user.firstName :
                        appointment.customerInfo.name.split(' ')[0],
                    lastName: appointment.user ?
                        appointment.user.lastName :
                        appointment.customerInfo.name.split(' ').slice(1).join(' ')
                };
                const appointmentInfo = {
                    _id: appointment._id,
                    service: appointment.service ? appointment.service.name : 'Unknown Service',
                    date: appointment.date,
                    time: appointment.time,
                    status: appointment.status
                };
                await emailService.sendAppointmentConfirmation(userInfo, appointmentInfo);
            }
            catch (emailError) {
                console.error('Failed to send confirmation email:', emailError);
                // Don't fail the request if email fails
            }
        }
        const responseData = {
            id: appointment._id,
            user: appointment.user ? {
                id: appointment.user._id,
                name: `${appointment.user.firstName || ''} ${appointment.user.lastName || ''}`.trim(),
                email: appointment.user.email,
                phone: appointment.user.phone
            } : null,
            customerInfo: appointment.customerInfo,
            service: appointment.service ? {
                id: appointment.service._id,
                name: appointment.service.name,
                price: appointment.service.price,
                duration: appointment.service.duration,
                category: appointment.service.category
            } : null,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            message: appointment.message,
            createdAt: appointment.createdAt,
            emailSent: sendConfirmation
        };
        (0, response_1.sendSuccess)(res, 'Appointment created successfully', responseData);
    }
    catch (error) {
        console.error('Create appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/appointments/analytics - Get appointment analytics
router.get('/appointments/analytics', async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        const dateFilter = {};
        if (startDate && endDate) {
            dateFilter.date = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        }
        // Get appointment statistics by status
        const statusStats = await models_1.Appointment.aggregate([
            { $match: dateFilter },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get appointments by date for chart
        const dailyStats = await models_1.Appointment.aggregate([
            { $match: dateFilter },
            {
                $group: {
                    _id: {
                        $dateToString: { format: '%Y-%m-%d', date: '$date' }
                    },
                    count: { $sum: 1 },
                    confirmed: {
                        $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
                    },
                    completed: {
                        $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
                    },
                    cancelled: {
                        $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
                    }
                }
            },
            { $sort: { '_id': 1 } }
        ]);
        // Get service popularity
        const serviceStats = await models_1.Appointment.aggregate([
            { $match: dateFilter },
            {
                $lookup: {
                    from: 'services',
                    localField: 'service',
                    foreignField: '_id',
                    as: 'serviceInfo'
                }
            },
            { $unwind: '$serviceInfo' },
            {
                $group: {
                    _id: '$service',
                    serviceName: { $first: '$serviceInfo.name' },
                    count: { $sum: 1 },
                    revenue: { $sum: '$serviceInfo.price' }
                }
            },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]);
        // Calculate revenue metrics
        const revenueStats = await models_1.Appointment.aggregate([
            { $match: { ...dateFilter, status: 'completed' } },
            {
                $lookup: {
                    from: 'services',
                    localField: 'service',
                    foreignField: '_id',
                    as: 'serviceInfo'
                }
            },
            { $unwind: '$serviceInfo' },
            {
                $group: {
                    _id: null,
                    totalRevenue: { $sum: '$serviceInfo.price' },
                    averageRevenue: { $avg: '$serviceInfo.price' },
                    totalAppointments: { $sum: 1 }
                }
            }
        ]);
        // Get today's appointments
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const todayAppointments = await models_1.Appointment.countDocuments({
            date: { $gte: today, $lt: tomorrow }
        });
        const responseData = {
            statusStats: statusStats.reduce((acc, stat) => {
                acc[stat._id] = stat.count;
                return acc;
            }, {}),
            dailyStats,
            serviceStats,
            revenueStats: revenueStats[0] || {
                totalRevenue: 0,
                averageRevenue: 0,
                totalAppointments: 0
            },
            todayAppointments,
            totalAppointments: await models_1.Appointment.countDocuments(dateFilter)
        };
        (0, response_1.sendSuccess)(res, 'Appointment analytics retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get appointment analytics error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/appointments/bulk-status - Bulk update appointment status
router.put('/appointments/bulk-status', async (req, res) => {
    try {
        const { appointmentIds, status } = req.body;
        if (!Array.isArray(appointmentIds) || appointmentIds.length === 0) {
            (0, response_1.sendError)(res, 'Appointment IDs array is required', undefined, 400);
            return;
        }
        if (!['pending', 'confirmed', 'completed', 'cancelled'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status', undefined, 400);
            return;
        }
        const result = await models_1.Appointment.updateMany({ _id: { $in: appointmentIds } }, { status, updatedAt: new Date() });
        (0, response_1.sendSuccess)(res, 'Appointments updated successfully', {
            updatedCount: result.modifiedCount,
            status
        });
    }
    catch (error) {
        console.error('Bulk update appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/payment-confirmations - Get all payment confirmations with pagination and filters
router.get('/payment-confirmations', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status;
        const query = {};
        if (status) {
            query.status = status;
        }
        const paymentConfirmations = await models_1.PaymentConfirmation.find(query)
            .populate('user', 'firstName lastName email phone')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name price' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit);
        const total = await models_1.PaymentConfirmation.countDocuments(query);
        const formattedConfirmations = paymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            user: {
                id: confirmation.user._id,
                name: `${confirmation.user.firstName} ${confirmation.user.lastName}`,
                email: confirmation.user.email,
                phone: confirmation.user.phone
            },
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            appointmentServicePrice: confirmation.appointment?.service?.price,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            proofImage: confirmation.proofImage,
            notes: confirmation.notes,
            status: confirmation.status,
            verifiedAt: confirmation.verifiedAt,
            rejectionReason: confirmation.rejectionReason,
            createdAt: confirmation.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'Payment confirmations retrieved successfully', {
            paymentConfirmations: formattedConfirmations,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Get admin payment confirmations error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/payment-confirmations/:id/status - Update payment confirmation status
router.put('/payment-confirmations/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, rejectionReason } = req.body;
        if (!['verified', 'rejected'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
            return;
        }
        if (status === 'rejected' && !rejectionReason) {
            (0, response_1.sendError)(res, 'Rejection reason is required when rejecting payment confirmation', undefined, 400);
            return;
        }
        const paymentConfirmation = await models_1.PaymentConfirmation.findById(id)
            .populate('user', 'firstName lastName email')
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ]);
        if (!paymentConfirmation) {
            (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
            return;
        }
        paymentConfirmation.status = status;
        paymentConfirmation.verifiedBy = req.user._id;
        paymentConfirmation.verifiedAt = new Date();
        if (status === 'rejected') {
            paymentConfirmation.rejectionReason = rejectionReason;
        }
        await paymentConfirmation.save();
        const responseData = {
            id: paymentConfirmation._id,
            status: paymentConfirmation.status,
            user: {
                name: `${paymentConfirmation.user.firstName} ${paymentConfirmation.user.lastName}`,
                email: paymentConfirmation.user.email
            },
            amount: paymentConfirmation.amount,
            paymentMethod: paymentConfirmation.paymentMethod,
            verifiedAt: paymentConfirmation.verifiedAt,
            rejectionReason: paymentConfirmation.rejectionReason,
            updatedAt: paymentConfirmation.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Payment confirmation status updated successfully', responseData);
    }
    catch (error) {
        console.error('Update payment confirmation status error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/customers - Get all customers with filtering and pagination
router.get('/customers', async (req, res) => {
    try {
        const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const filter = { role: 'user' };
        // Search filter
        if (search) {
            filter.$or = [
                { firstName: { $regex: search, $options: 'i' } },
                { lastName: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const customers = await models_1.User.find(filter)
            .select('-password')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await models_1.User.countDocuments(filter);
        // Get appointment statistics for each customer
        const customerIds = customers.map(customer => customer._id);
        const appointmentStats = await models_1.Appointment.aggregate([
            { $match: { user: { $in: customerIds } } },
            {
                $group: {
                    _id: '$user',
                    totalAppointments: { $sum: 1 },
                    totalSpent: { $sum: '$totalPrice' },
                    lastAppointment: { $max: '$date' }
                }
            }
        ]);
        // Create a map for quick lookup
        const statsMap = new Map();
        appointmentStats.forEach(stat => {
            statsMap.set(stat._id.toString(), stat);
        });
        const responseData = {
            customers: customers.map(customer => {
                const stats = statsMap.get(customer._id.toString()) || {
                    totalAppointments: 0,
                    totalSpent: 0,
                    lastAppointment: null
                };
                return {
                    id: customer._id,
                    firstName: customer.firstName,
                    lastName: customer.lastName,
                    email: customer.email,
                    phone: customer.phone,
                    totalAppointments: stats.totalAppointments,
                    totalSpent: stats.totalSpent,
                    lastAppointment: stats.lastAppointment,
                    status: customer.isVerified ? 'active' : 'inactive',
                    createdAt: customer.createdAt,
                    updatedAt: customer.updatedAt
                };
            }),
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        };
        (0, response_1.sendSuccess)(res, 'Customers retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get customers error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/customers/:id - Get single customer details
router.get('/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const customer = await models_1.User.findById(id).select('-password');
        if (!customer) {
            (0, response_1.sendError)(res, 'Customer not found', undefined, 404);
            return;
        }
        // Get customer's appointments
        const appointments = await models_1.Appointment.find({ user: id })
            .populate('service', 'name price duration category')
            .sort({ date: -1 })
            .limit(10);
        // Get customer statistics
        const stats = await models_1.Appointment.aggregate([
            { $match: { user: customer._id } },
            {
                $group: {
                    _id: null,
                    totalAppointments: { $sum: 1 },
                    totalSpent: { $sum: '$totalPrice' },
                    lastAppointment: { $max: '$date' }
                }
            }
        ]);
        const customerStats = stats[0] || {
            totalAppointments: 0,
            totalSpent: 0,
            lastAppointment: null
        };
        const responseData = {
            id: customer._id,
            firstName: customer.firstName,
            lastName: customer.lastName,
            email: customer.email,
            phone: customer.phone,
            isVerified: customer.isVerified,
            notificationPreferences: customer.notificationPreferences,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt,
            statistics: customerStats,
            recentAppointments: appointments.map(apt => ({
                id: apt._id,
                service: apt.service ? {
                    id: apt.service._id,
                    name: apt.service.name,
                    price: apt.service.price
                } : null,
                date: apt.date,
                time: apt.time,
                status: apt.status,
                totalPrice: apt.totalPrice
            }))
        };
        (0, response_1.sendSuccess)(res, 'Customer details retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get customer details error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/customers/:id - Update customer
router.put('/customers/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { firstName, lastName, phone, notificationPreferences } = req.body;
        const customer = await models_1.User.findById(id);
        if (!customer) {
            (0, response_1.sendError)(res, 'Customer not found', undefined, 404);
            return;
        }
        // Update fields if provided
        if (firstName)
            customer.firstName = firstName;
        if (lastName)
            customer.lastName = lastName;
        if (phone)
            customer.phone = phone;
        if (notificationPreferences) {
            customer.notificationPreferences = {
                ...customer.notificationPreferences,
                ...notificationPreferences
            };
        }
        await customer.save();
        const responseData = {
            id: customer._id,
            firstName: customer.firstName,
            lastName: customer.lastName,
            email: customer.email,
            phone: customer.phone,
            isVerified: customer.isVerified,
            notificationPreferences: customer.notificationPreferences,
            updatedAt: customer.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Customer updated successfully', responseData);
    }
    catch (error) {
        console.error('Update customer error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/services - Get all services with filtering and pagination
router.get('/services', async (req, res) => {
    try {
        const { page = 1, limit = 20, search, category, isActive, sortBy = 'name', sortOrder = 'asc' } = req.query;
        const filter = {};
        // Category filter
        if (category && category !== 'all') {
            filter.category = category;
        }
        // Active filter
        if (isActive !== undefined) {
            filter.isActive = isActive === 'true';
        }
        // Search filter
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { category: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const services = await models_1.Service.find(filter)
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await models_1.Service.countDocuments(filter);
        const responseData = {
            services: services.map(service => ({
                id: service._id,
                name: service.name,
                description: service.description,
                category: service.category,
                duration: service.duration,
                price: service.price,
                isActive: service.isActive,
                image: service.image,
                images: service.images,
                createdAt: service.createdAt,
                updatedAt: service.updatedAt
            })),
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        };
        (0, response_1.sendSuccess)(res, 'Services retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get services error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/services/:id - Get single service details
router.get('/services/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const service = await models_1.Service.findById(id);
        if (!service) {
            (0, response_1.sendError)(res, 'Service not found', undefined, 404);
            return;
        }
        const responseData = {
            id: service._id,
            name: service.name,
            description: service.description,
            category: service.category,
            duration: service.duration,
            price: service.price,
            isActive: service.isActive,
            image: service.image,
            images: service.images,
            createdAt: service.createdAt,
            updatedAt: service.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Service details retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get service details error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// POST /api/v2/admin/services - Create new service
router.post('/services', async (req, res) => {
    try {
        const { name, description, category, duration, price, isActive = true, image, images } = req.body;
        // Validate required fields
        if (!name || !description || !category || !duration || price === undefined) {
            (0, response_1.sendError)(res, 'Name, description, category, duration, and price are required', undefined, 400);
            return;
        }
        const service = new models_1.Service({
            name,
            description,
            category,
            duration,
            price,
            isActive,
            image,
            images: images || []
        });
        await service.save();
        const responseData = {
            id: service._id,
            name: service.name,
            description: service.description,
            category: service.category,
            duration: service.duration,
            price: service.price,
            isActive: service.isActive,
            image: service.image,
            images: service.images,
            createdAt: service.createdAt,
            updatedAt: service.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Service created successfully', responseData);
    }
    catch (error) {
        console.error('Create service error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/services/:id - Update service
router.put('/services/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, category, duration, price, isActive, image, images } = req.body;
        const service = await models_1.Service.findById(id);
        if (!service) {
            (0, response_1.sendError)(res, 'Service not found', undefined, 404);
            return;
        }
        // Update fields if provided
        if (name)
            service.name = name;
        if (description)
            service.description = description;
        if (category)
            service.category = category;
        if (duration !== undefined)
            service.duration = duration;
        if (price !== undefined)
            service.price = price;
        if (isActive !== undefined)
            service.isActive = isActive;
        if (image !== undefined)
            service.image = image;
        if (images !== undefined)
            service.images = images;
        await service.save();
        const responseData = {
            id: service._id,
            name: service.name,
            description: service.description,
            category: service.category,
            duration: service.duration,
            price: service.price,
            isActive: service.isActive,
            image: service.image,
            images: service.images,
            updatedAt: service.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Service updated successfully', responseData);
    }
    catch (error) {
        console.error('Update service error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/admin/services/:id - Delete service
router.delete('/services/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const service = await models_1.Service.findById(id);
        if (!service) {
            (0, response_1.sendError)(res, 'Service not found', undefined, 404);
            return;
        }
        // Check if service is used in any appointments
        const appointmentCount = await models_1.Appointment.countDocuments({ service: id });
        if (appointmentCount > 0) {
            // Instead of deleting, mark as inactive
            service.isActive = false;
            await service.save();
            (0, response_1.sendSuccess)(res, 'Service deactivated successfully (has existing appointments)', {
                id: service._id,
                isActive: service.isActive
            });
        }
        else {
            // Safe to delete
            await models_1.Service.findByIdAndDelete(id);
            (0, response_1.sendSuccess)(res, 'Service deleted successfully', { id });
        }
    }
    catch (error) {
        console.error('Delete service error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/settings - Get all admin settings
router.get('/settings', async (_req, res) => {
    try {
        // Import models dynamically to avoid circular dependencies
        const { SiteSettings, PaymentSettings, ThemeSettings, SEO } = require('../../models');
        // Get all settings in parallel
        const [siteSettings, paymentSettings, themeSettings, seoSettings] = await Promise.all([
            SiteSettings.findOne() || SiteSettings.create({}),
            PaymentSettings.findOne() || PaymentSettings.create({}),
            ThemeSettings.findOne({ isActive: true }) || ThemeSettings.create({}),
            SEO.findOne() || SEO.create({})
        ]);
        const responseData = {
            site: siteSettings,
            payment: paymentSettings,
            theme: themeSettings,
            seo: seoSettings
        };
        (0, response_1.sendSuccess)(res, 'Settings retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get settings error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/settings/site - Update site settings
router.put('/settings/site', async (req, res) => {
    try {
        const { SiteSettings } = require('../../models');
        const updateData = req.body;
        let settings = await SiteSettings.findOne();
        if (!settings) {
            settings = await SiteSettings.create(updateData);
        }
        else {
            // Deep merge the update data
            Object.keys(updateData).forEach(key => {
                if (typeof updateData[key] === 'object' && updateData[key] !== null) {
                    settings[key] = { ...settings[key], ...updateData[key] };
                }
                else {
                    settings[key] = updateData[key];
                }
            });
            await settings.save();
        }
        (0, response_1.sendSuccess)(res, 'Site settings updated successfully', settings);
    }
    catch (error) {
        console.error('Update site settings error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/settings/payment - Update payment settings
router.put('/settings/payment', async (req, res) => {
    try {
        const { PaymentSettings } = require('../../models');
        const updateData = req.body;
        let settings = await PaymentSettings.findOne();
        if (!settings) {
            settings = await PaymentSettings.create(updateData);
        }
        else {
            Object.assign(settings, updateData);
            await settings.save();
        }
        (0, response_1.sendSuccess)(res, 'Payment settings updated successfully', settings);
    }
    catch (error) {
        console.error('Update payment settings error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/settings/theme - Update theme settings
router.put('/settings/theme', async (req, res) => {
    try {
        const { ThemeSettings } = require('../../models');
        const updateData = req.body;
        let settings = await ThemeSettings.findOne({ isActive: true });
        if (!settings) {
            settings = await ThemeSettings.create({ ...updateData, isActive: true });
        }
        else {
            Object.assign(settings, updateData);
            await settings.save();
        }
        (0, response_1.sendSuccess)(res, 'Theme settings updated successfully', settings);
    }
    catch (error) {
        console.error('Update theme settings error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/settings/seo - Update SEO settings
router.put('/settings/seo', async (req, res) => {
    try {
        const { SEO } = require('../../models');
        const updateData = req.body;
        let settings = await SEO.findOne();
        if (!settings) {
            settings = await SEO.create(updateData);
        }
        else {
            Object.assign(settings, updateData);
            await settings.save();
        }
        (0, response_1.sendSuccess)(res, 'SEO settings updated successfully', settings);
    }
    catch (error) {
        console.error('Update SEO settings error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/admin/categories - Get all service categories
router.get('/categories', async (req, res) => {
    try {
        // Get unique categories from services
        const categories = await models_1.Service.distinct('category', { isActive: true });
        // Get category statistics
        const categoryStats = await models_1.Service.aggregate([
            { $match: { isActive: true } },
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 },
                    avgPrice: { $avg: '$price' },
                    minPrice: { $min: '$price' },
                    maxPrice: { $max: '$price' }
                }
            },
            { $sort: { _id: 1 } }
        ]);
        const categoriesWithStats = categories.map(category => {
            const stats = categoryStats.find(stat => stat._id === category);
            return {
                name: category,
                serviceCount: stats?.count || 0,
                averagePrice: stats?.avgPrice || 0,
                priceRange: {
                    min: stats?.minPrice || 0,
                    max: stats?.maxPrice || 0
                }
            };
        });
        (0, response_1.sendSuccess)(res, 'Categories retrieved successfully', {
            categories: categoriesWithStats,
            total: categories.length
        });
    }
    catch (error) {
        console.error('Get categories error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/categories/rename - Rename a category
router.put('/categories/rename', async (req, res) => {
    try {
        const { oldName, newName } = req.body;
        if (!oldName || !newName) {
            (0, response_1.sendError)(res, 'Both oldName and newName are required', undefined, 400);
            return;
        }
        if (oldName === newName) {
            (0, response_1.sendError)(res, 'Old name and new name cannot be the same', undefined, 400);
            return;
        }
        // Check if new category name already exists
        const existingCategory = await models_1.Service.findOne({ category: newName, isActive: true });
        if (existingCategory) {
            (0, response_1.sendError)(res, 'A category with the new name already exists', undefined, 409);
            return;
        }
        // Update all services with the old category name
        const result = await models_1.Service.updateMany({ category: oldName }, { $set: { category: newName } });
        if (result.matchedCount === 0) {
            (0, response_1.sendError)(res, 'No services found with the specified category name', undefined, 404);
            return;
        }
        (0, response_1.sendSuccess)(res, 'Category renamed successfully', {
            oldName,
            newName,
            servicesUpdated: result.modifiedCount
        });
    }
    catch (error) {
        console.error('Rename category error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/admin/categories/:categoryName - Delete a category (moves services to 'Uncategorized')
router.delete('/categories/:categoryName', async (req, res) => {
    try {
        const { categoryName } = req.params;
        if (!categoryName) {
            (0, response_1.sendError)(res, 'Category name is required', undefined, 400);
            return;
        }
        // Update all services in this category to 'Uncategorized'
        const result = await models_1.Service.updateMany({ category: categoryName }, { $set: { category: 'Uncategorized' } });
        if (result.matchedCount === 0) {
            (0, response_1.sendError)(res, 'No services found with the specified category name', undefined, 404);
            return;
        }
        (0, response_1.sendSuccess)(res, 'Category deleted successfully', {
            deletedCategory: categoryName,
            servicesMovedToUncategorized: result.modifiedCount
        });
    }
    catch (error) {
        console.error('Delete category error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// ==================== REVIEW MANAGEMENT ====================
// GET /api/v2/admin/reviews - Get all reviews with filtering and pagination
router.get('/reviews', async (req, res) => {
    try {
        const { page = 1, limit = 20, status, service, rating, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build filter query
        const filter = {};
        if (status && status !== 'all') {
            filter.status = status;
        }
        if (service) {
            filter.service = service;
        }
        if (rating) {
            filter.rating = parseInt(rating);
        }
        // Build sort query
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const reviews = await models_1.Review.find(filter)
            .populate('user', 'firstName lastName email')
            .populate('service', 'name category')
            .populate('appointment', 'date time status')
            .sort(sort)
            .skip(skip)
            .limit(limitNum);
        const total = await models_1.Review.countDocuments(filter);
        // Get review statistics
        const stats = await models_1.Review.aggregate([
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    pendingReviews: {
                        $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
                    },
                    approvedReviews: {
                        $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
                    },
                    rejectedReviews: {
                        $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] }
                    }
                }
            }
        ]);
        (0, response_1.sendSuccess)(res, 'Reviews retrieved successfully', {
            reviews,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum)
            },
            stats: stats[0] || {
                totalReviews: 0,
                averageRating: 0,
                pendingReviews: 0,
                approvedReviews: 0,
                rejectedReviews: 0
            }
        });
    }
    catch (error) {
        console.error('Get admin reviews error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/reviews/:reviewId/status - Update review status
router.put('/reviews/:reviewId/status', async (req, res) => {
    try {
        const { reviewId } = req.params;
        const { status, adminNote } = req.body;
        if (!['pending', 'approved', 'rejected'].includes(status)) {
            (0, response_1.sendError)(res, 'Invalid status. Must be pending, approved, or rejected', undefined, 400);
            return;
        }
        const review = await models_1.Review.findById(reviewId);
        if (!review) {
            (0, response_1.sendError)(res, 'Review not found', undefined, 404);
            return;
        }
        review.status = status;
        if (adminNote) {
            review.adminResponse = adminNote;
            review.adminResponseDate = new Date();
        }
        await review.save();
        const updatedReview = await models_1.Review.findById(reviewId)
            .populate('user', 'firstName lastName email')
            .populate('service', 'name category')
            .populate('appointment', 'date time status');
        (0, response_1.sendSuccess)(res, 'Review status updated successfully', updatedReview);
    }
    catch (error) {
        console.error('Update review status error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/admin/reviews/:reviewId - Delete a review
router.delete('/reviews/:reviewId', async (req, res) => {
    try {
        const { reviewId } = req.params;
        const review = await models_1.Review.findByIdAndDelete(reviewId);
        if (!review) {
            (0, response_1.sendError)(res, 'Review not found', undefined, 404);
            return;
        }
        (0, response_1.sendSuccess)(res, 'Review deleted successfully', { deletedId: reviewId });
    }
    catch (error) {
        console.error('Delete review error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/admin/reviews/:reviewId - Update review content (admin edit)
router.put('/reviews/:reviewId', async (req, res) => {
    try {
        const { reviewId } = req.params;
        const { title, comment, rating, adminResponse } = req.body;
        const review = await models_1.Review.findById(reviewId);
        if (!review) {
            (0, response_1.sendError)(res, 'Review not found', undefined, 404);
            return;
        }
        // Update fields if provided
        if (title !== undefined)
            review.title = title;
        if (comment !== undefined)
            review.comment = comment;
        if (rating !== undefined) {
            if (rating < 1 || rating > 5) {
                (0, response_1.sendError)(res, 'Rating must be between 1 and 5', undefined, 400);
                return;
            }
            review.rating = rating;
        }
        if (adminResponse !== undefined) {
            review.adminResponse = adminResponse;
            review.adminResponseDate = new Date();
        }
        await review.save();
        const updatedReview = await models_1.Review.findById(reviewId)
            .populate('user', 'firstName lastName email')
            .populate('service', 'name category')
            .populate('appointment', 'date time status');
        (0, response_1.sendSuccess)(res, 'Review updated successfully', updatedReview);
    }
    catch (error) {
        console.error('Update review error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
