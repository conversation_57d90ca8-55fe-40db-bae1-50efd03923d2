import React, { useState, useEffect } from 'react';
import { type ServiceFilters as ServiceFiltersType } from '../../utils/serviceAPI';

interface ServiceFiltersProps {
  filters: ServiceFiltersType;
  categories: string[];
  onFilterChange: (filters: Partial<ServiceFiltersType>) => void;
  viewMode: 'grid' | 'table';
  onViewModeChange: (mode: 'grid' | 'table') => void;
}

export default function ServiceFilters({
  filters,
  categories,
  onFilterChange,
  viewMode,
  onViewModeChange
}: ServiceFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [searchTimeout, setSearchTimeout] = useState<number | null>(null);

  // Debounced search
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = window.setTimeout(() => {
      onFilterChange({ search: searchTerm });
    }, 300);

    setSearchTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [searchTerm]);

  // Update search term when filters change externally
  useEffect(() => {
    setSearchTerm(filters.search || '');
  }, [filters.search]);

  // Handle filter changes
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange({ category: e.target.value });
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFilterChange({ 
      isActive: value === '' ? undefined : value === 'true' 
    });
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [sortBy, sortOrder] = e.target.value.split(':');
    onFilterChange({ 
      sortBy: sortBy as any, 
      sortOrder: sortOrder as 'asc' | 'desc' 
    });
  };

  const handleLimitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange({ limit: parseInt(e.target.value) });
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchTerm('');
    onFilterChange({
      search: '',
      category: '',
      isActive: undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      page: 1
    });
  };

  // Check if any filters are active
  const hasActiveFilters = !!(
    filters.search ||
    filters.category ||
    filters.isActive !== undefined
  );

  return (
    <div className="appointment-filters">
      <div className="filters-row">
        {/* Search */}
        <div className="filter-group">
          <label>Search Services</label>
          <div className="search-input-container">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or description..."
              className="search-input"
            />
            <svg className="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Category Filter */}
        <div className="filter-group">
          <label>Category</label>
          <select
            value={filters.category || ''}
            onChange={handleCategoryChange}
            className="filter-select"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Status Filter */}
        <div className="filter-group">
          <label>Status</label>
          <select
            value={filters.isActive === undefined ? '' : filters.isActive.toString()}
            onChange={handleStatusChange}
            className="filter-select"
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>
        </div>

        {/* Sort */}
        <div className="filter-group">
          <label>Sort By</label>
          <select
            value={`${filters.sortBy}:${filters.sortOrder}`}
            onChange={handleSortChange}
            className="filter-select"
          >
            <option value="createdAt:desc">Newest First</option>
            <option value="createdAt:asc">Oldest First</option>
            <option value="name:asc">Name A-Z</option>
            <option value="name:desc">Name Z-A</option>
            <option value="price:asc">Price Low-High</option>
            <option value="price:desc">Price High-Low</option>
            <option value="category:asc">Category A-Z</option>
            <option value="duration:asc">Duration Short-Long</option>
            <option value="duration:desc">Duration Long-Short</option>
          </select>
        </div>

        {/* Items per page */}
        <div className="filter-group">
          <label>Per Page</label>
          <select
            value={filters.limit}
            onChange={handleLimitChange}
            className="filter-select"
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="filter-group">
          <label>View</label>
          <div className="view-mode-toggle">
            <button
              onClick={() => onViewModeChange('table')}
              className={viewMode === 'table' ? 'active' : ''}
              title="Table View"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 6h18m-9 8h9" />
              </svg>
            </button>
            <button
              onClick={() => onViewModeChange('grid')}
              className={viewMode === 'grid' ? 'active' : ''}
              title="Grid View"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <div className="filter-group">
            <label>&nbsp;</label>
            <button
              onClick={handleClearFilters}
              className="btn btn-outline btn-sm"
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="results-summary">
          <div className="active-filters">
            {filters.search && (
              <span className="filter-tag">
                Search: "{filters.search}"
              </span>
            )}
            {filters.category && (
              <span className="filter-tag">
                Category: {filters.category}
              </span>
            )}
            {filters.isActive !== undefined && (
              <span className="filter-tag">
                Status: {filters.isActive ? 'Active' : 'Inactive'}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Quick Filters */}
      <div className="quick-filters">
        <span className="quick-filters-label">Quick Filters:</span>
        <button
          onClick={() => onFilterChange({ isActive: true })}
          className="quick-filter-btn"
        >
          Active Services
        </button>
        <button
          onClick={() => onFilterChange({ isActive: false })}
          className="quick-filter-btn"
        >
          Inactive Services
        </button>
        <button
          onClick={() => onFilterChange({ sortBy: 'price', sortOrder: 'desc' })}
          className="quick-filter-btn"
        >
          Highest Priced
        </button>
        <button
          onClick={() => onFilterChange({ sortBy: 'createdAt', sortOrder: 'desc' })}
          className="quick-filter-btn"
        >
          Recently Added
        </button>
      </div>
    </div>
  );
}
