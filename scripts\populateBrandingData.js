const mongoose = require('mongoose');
require('dotenv').config();

// Define the Branding schema (matching the backend model)
const brandingSchema = new mongoose.Schema({
  global: {
    siteName: { type: String, default: '' },
    tagline: { type: String, default: '' },
    logo: { type: String, default: '' },
    favicon: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' },
    address: { type: String, default: '' },
    instagram: { type: String, default: '' },
    facebook: { type: String, default: '' },
    twitter: { type: String, default: '' },
    youtube: { type: String, default: '' }
  },
  home: {
    heroTitle: { type: String, default: '' },
    heroSubtitle: { type: String, default: '' },
    heroImage: { type: String, default: '' },
    aboutTitle: { type: String, default: '' },
    aboutText: { type: String, default: '' },
    featuredServices: [{ type: String }],
    testimonialHeading: { type: String, default: '' }
  },
  services: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    pageDescription: { type: String, default: '' },
    serviceCategories: [{ type: String }],
    serviceLocMaintenance: { type: String, default: '' },
    serviceLocMaintenanceDesc: { type: String, default: '' },
    serviceStarterLocs: { type: String, default: '' },
    serviceStarterLocsDesc: { type: String, default: '' },
    serviceLocStyling: { type: String, default: '' },
    serviceLocStylingDesc: { type: String, default: '' },
    serviceNaturalHairCare: { type: String, default: '' },
    serviceNaturalHairCareDesc: { type: String, default: '' }
  },
  shop: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    pageDescription: { type: String, default: '' },
    featuredCollectionTitle: { type: String, default: '' }
  },
  consultation: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    pageDescription: { type: String, default: '' },
    formTitle: { type: String, default: '' },
    formSubtitle: { type: String, default: '' }
  },
  login: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' }
  },
  signup: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    agreeTermsText: { type: String, default: '' },
    termsLinkText: { type: String, default: '' },
    andText: { type: String, default: '' },
    privacyLinkText: { type: String, default: '' }
  },
  cart: {
    pageTitle: { type: String, default: '' },
    emptyCartMessage: { type: String, default: '' },
    freeShippingThreshold: { type: String, default: '' },
    shippingCalculated: { type: String, default: '' }
  },
  productDetail: {
    addToCartButton: { type: String, default: '' },
    quantityLabel: { type: String, default: '' },
    overviewTab: { type: String, default: '' },
    ingredientsTab: { type: String, default: '' },
    reviewsTab: { type: String, default: '' }
  },
  footer: {
    description: { type: String, default: '' },
    copyrightText: { type: String, default: '' },
    quickLinks: [{ type: String }],
    contact: { type: String, default: '' },
    followUs: { type: String, default: '' }
  },
  dashboard: {
    welcomeMessage: { type: String, default: '' },
    overviewTitle: { type: String, default: '' },
    appointmentsTitle: { type: String, default: '' },
    ordersTitle: { type: String, default: '' },
    favoritesTitle: { type: String, default: '' },
    profileTitle: { type: String, default: '' },
    nextAppointment: { type: String, default: '' },
    recentOrders: { type: String, default: '' },
    loyaltyTitle: { type: String, default: '' }
  },
  buttons: {
    bookNow: { type: String, default: '' },
    shopNow: { type: String, default: '' },
    learnMore: { type: String, default: '' },
    viewAll: { type: String, default: '' },
    continueShopping: { type: String, default: '' },
    proceedToCheckout: { type: String, default: '' },
    addToCart: { type: String, default: '' },
    scheduleConsultation: { type: String, default: '' },
    writeReview: { type: String, default: '' }
  },
  navigation: {
    home: { type: String, default: '' },
    services: { type: String, default: '' },
    shop: { type: String, default: '' },
    consultation: { type: String, default: '' },
    contact: { type: String, default: '' },
    login: { type: String, default: '' },
    signup: { type: String, default: '' },
    dashboard: { type: String, default: '' }
  },
  contact: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    pageDescription: { type: String, default: '' },
    phoneLabel: { type: String, default: '' },
    emailLabel: { type: String, default: '' },
    businessHoursLabel: { type: String, default: '' },
    businessHours: { type: String, default: '' },
    addressLabel: { type: String, default: '' },
    address: { type: String, default: '' },
    getInTouchTitle: { type: String, default: '' },
    getInTouchSubtitle: { type: String, default: '' }
  },
  reviews: {
    sectionTitle: { type: String, default: '' },
    writeReviewButton: { type: String, default: '' },
    noReviewsMessage: { type: String, default: '' },
    formTitle: { type: String, default: '' },
    formSubtitle: { type: String, default: '' },
    ratingLabel: { type: String, default: '' },
    titleLabel: { type: String, default: '' },
    titlePlaceholder: { type: String, default: '' },
    commentLabel: { type: String, default: '' },
    commentPlaceholder: { type: String, default: '' },
    submitButton: { type: String, default: '' },
    submittingText: { type: String, default: '' },
    successMessage: { type: String, default: '' },
    pendingApprovalMessage: { type: String, default: '' },
    editReviewTitle: { type: String, default: '' },
    updateButton: { type: String, default: '' },
    updatingText: { type: String, default: '' },
    deleteConfirmMessage: { type: String, default: '' },
    verifiedPurchaseLabel: { type: String, default: '' },
    helpfulButton: { type: String, default: '' },
    reportButton: { type: String, default: '' },
    sortByLabel: { type: String, default: '' },
    sortNewest: { type: String, default: '' },
    sortOldest: { type: String, default: '' },
    sortHighestRated: { type: String, default: '' },
    sortLowestRated: { type: String, default: '' },
    filterByRating: { type: String, default: '' },
    allRatings: { type: String, default: '' },
    showMoreButton: { type: String, default: '' },
    showLessButton: { type: String, default: '' }
  },
  paymentConfirmation: {
    pageTitle: { type: String, default: '' },
    pageSubtitle: { type: String, default: '' },
    pageDescription: { type: String, default: '' },
    uploadTitle: { type: String, default: '' },
    uploadSubtitle: { type: String, default: '' },
    uploadInstructions: { type: String, default: '' },
    dragDropText: { type: String, default: '' },
    browseFilesText: { type: String, default: '' },
    supportedFormats: { type: String, default: '' },
    maxFileSize: { type: String, default: '' },
    orderReferenceLabel: { type: String, default: '' },
    appointmentReferenceLabel: { type: String, default: '' },
    amountLabel: { type: String, default: '' },
    paymentMethodLabel: { type: String, default: '' },
    notesLabel: { type: String, default: '' },
    notesPlaceholder: { type: String, default: '' },
    submitButton: { type: String, default: '' },
    submittingText: { type: String, default: '' },
    successMessage: { type: String, default: '' },
    uploadingText: { type: String, default: '' },
    previewTitle: { type: String, default: '' },
    removeImageButton: { type: String, default: '' },
    retryUploadButton: { type: String, default: '' }
  },
  testimonials: {
    title: { type: String, default: '' },
    subtitle: { type: String, default: '' }
  },
  reviews: {
    title: { type: String, default: '' }
  },
  messages: {
    loading: { type: String, default: '' },
    error: { type: String, default: '' },
    notFound: { type: String, default: '' },
    comingSoon: { type: String, default: '' },
    cartShipping: { type: String, default: '' }
  },
  business: {
    address: {
      street: { type: String, default: '' },
      city: { type: String, default: '' },
      state: { type: String, default: '' },
      zip: { type: String, default: '' },
      full: { type: String, default: '' }
    },
    social: {
      instagram: { type: String, default: '' },
      facebook: { type: String, default: '' },
      twitter: { type: String, default: '' }
    },
    hours: {
      monday: { type: String, default: '' },
      tuesday: { type: String, default: '' },
      wednesday: { type: String, default: '' },
      thursday: { type: String, default: '' },
      friday: { type: String, default: '' },
      saturday: { type: String, default: '' },
      sunday: { type: String, default: '' }
    },
    name: { type: String, default: '' },
    tagline: { type: String, default: '' },
    description: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' }
  },
  theme: {
    colors: {
      primary: { type: String, default: '' },
      secondary: { type: String, default: '' },
      accent: { type: String, default: '' },
      background: { type: String, default: '' },
      text: { type: String, default: '' },
      textSecondary: { type: String, default: '' }
    },
    fonts: {
      primary: { type: String, default: '' },
      secondary: { type: String, default: '' },
      heading: { type: String, default: '' }
    }
  },
  site: {
    seo: {
      title: { type: String, default: '' },
      description: { type: String, default: '' },
      keywords: { type: String, default: '' }
    },
    features: {
      onlineBooking: { type: Boolean, default: false },
      ecommerce: { type: Boolean, default: false },
      loyaltyProgram: { type: Boolean, default: false },
      giftCards: { type: Boolean, default: false },
      reviews: { type: Boolean, default: false },
      blog: { type: Boolean, default: false }
    }
  },
  legal: {
    privacyPolicy: {
      title: { type: String, default: '' },
      content: { type: String, default: '' },
      lastUpdated: { type: Date, default: Date.now }
    },
    termsOfService: {
      title: { type: String, default: '' },
      content: { type: String, default: '' },
      lastUpdated: { type: Date, default: Date.now }
    },
    copyrightText: { type: String, default: '' },
    companyName: { type: String, default: '' }
  }
}, {
  timestamps: true
});

const Branding = mongoose.model('Branding', brandingSchema);

// The complete branding data to populate
const brandingData = {
  global: {
    siteName: "Dammyspicy Beauty",
    tagline: "Professional Hair Care Services",
    logo: "",
    favicon: "",
    phone: "(*************",
    email: "<EMAIL>",
    address: "Indianapolis, IN",
    instagram: "https://instagram.com/dammyspicybeauty",
    facebook: "https://facebook.com/dammyspicybeauty",
    twitter: "https://twitter.com/dammyspicybeauty",
    youtube: ""
  },
  home: {
    heroTitle: "Transform Your Hair Journey",
    heroSubtitle: "Professional loc services and natural hair care with personalized attention",
    heroImage: "/images/hero-locs.jpg",
    aboutTitle: "About Me",
    aboutText: "Thank you for choosing Dammyspicy Beauty.\n\nMy name is Dammy, and I am a licensed cosmetologist specializing in hair care and beauty treatments for natural hair, including microlocs and more. Based in Indianapolis, IN, my passion is helping women embrace their natural beauty with confidence.\n\nMy main objective is to bring out the beauty in each individual, put smiles on faces, and create styles that reflect uniqueness and elegance.\n\nI'm excited to begin this healthy hair journey with you!",
    featuredServices: ["Starter Locs", "Loc Maintenance", "Natural Hair Care"],
    testimonialHeading: "What Our Clients Say"
  },
  services: {
    pageTitle: "Professional Hair Services",
    pageSubtitle: "Expert care for your natural hair journey",
    pageDescription: "From starter locs to maintenance and styling, we provide comprehensive care for your natural hair journey.",
    serviceCategories: ["Loc Services", "Natural Hair Care", "Styling", "Consultations"],
    serviceLocMaintenance: "Loc Maintenance",
    serviceLocMaintenanceDesc: "Professional maintenance to keep your locs healthy and looking their best",
    serviceStarterLocs: "Starter Locs",
    serviceStarterLocsDesc: "Begin your loc journey with expert guidance and professional techniques",
    serviceLocStyling: "Loc Styling",
    serviceLocStylingDesc: "Creative styling options to showcase your locs for any occasion",
    serviceNaturalHairCare: "Natural Hair Care",
    serviceNaturalHairCareDesc: "Comprehensive care for natural hair health and growth"
  },
  shop: {
    pageTitle: "Hair Care Products",
    pageSubtitle: "Premium products for healthy locs and natural hair",
    pageDescription: "Discover our curated collection of premium hair care products designed specifically for locs and natural hair.",
    featuredCollectionTitle: "Featured Products"
  },
  consultation: {
    pageTitle: "Book Your Consultation",
    pageSubtitle: "Start your hair journey with expert guidance",
    pageDescription: "Schedule a one-on-one consultation to discuss your hair goals, assess your hair type, and create a customized care plan.",
    formTitle: "Schedule Your Consultation",
    formSubtitle: "Tell us about your hair goals and we'll create a personalized plan"
  },
  login: {
    pageTitle: "Welcome Back",
    pageSubtitle: "Sign in to your account to manage appointments and orders",
    signInButton: "Sign In",
    signingInText: "Signing in...",
    noAccountText: "Don't have an account?",
    signUpLink: "Sign up here",
    forgotPasswordLink: "Forgot your password?"
  },
  signup: {
    pageTitle: "Join MicroLocs",
    pageSubtitle: "Create your account to book services and shop products",
    createAccountButton: "Create Account",
    creatingAccountText: "Creating account...",
    haveAccountText: "Already have an account?",
    signInLink: "Sign in here",
    agreeTermsText: "I agree to the",
    termsLinkText: "Terms of Service",
    andText: "and",
    privacyLinkText: "Privacy Policy"
  },
  cart: {
    pageTitle: "Shopping Cart",
    emptyCartMessage: "Your cart is empty. Discover our premium hair care products!",
    freeShippingThreshold: "Free shipping on orders over $50",
    shippingCalculated: "Shipping calculated at checkout"
  },
  productDetail: {
    addToCartButton: "Add to Cart",
    quantityLabel: "Quantity",
    overviewTab: "Overview",
    ingredientsTab: "Ingredients",
    reviewsTab: "Reviews"
  },
  footer: {
    description: "Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.",
    copyrightText: "© 2024 MicroLocs. All rights reserved.",
    quickLinks: ["Privacy Policy", "Terms of Service"],
    contact: "Contact Us",
    followUs: "Follow Us"
  },
  dashboard: {
    welcomeMessage: "Welcome back to your hair care journey!",
    overviewTitle: "Your Hair Care Overview",
    appointmentsTitle: "Your Appointments",
    ordersTitle: "Your Orders",
    favoritesTitle: "Your Favorites",
    profileTitle: "Profile Settings",
    nextAppointment: "Your Next Appointment",
    recentOrders: "Recent Orders",
    loyaltyTitle: "Loyalty Rewards"
  },
  buttons: {
    bookNow: "Book Now",
    shopNow: "Shop Now",
    learnMore: "Learn More",
    viewAll: "View All",
    continueShopping: "Continue Shopping",
    proceedToCheckout: "Proceed to Checkout",
    addToCart: "Add to Cart",
    scheduleConsultation: "Schedule Consultation",
    writeReview: "Write a Review"
  },
  navigation: {
    home: "Home",
    services: "Services",
    shop: "Shop",
    consultation: "Book Consultation",
    contact: "Contact",
    login: "Login",
    signup: "Sign Up",
    dashboard: "Dashboard"
  },
  contact: {
    pageTitle: "Contact Us",
    pageSubtitle: "Get in touch with our team",
    pageDescription: "We're here to help with any questions about our services or to schedule your appointment.",
    phoneLabel: "Phone",
    emailLabel: "Email",
    businessHoursLabel: "Business Hours",
    businessHours: "Monday - Saturday: 9:00 AM - 6:00 PM\nSunday: Closed",
    addressLabel: "Address",
    address: "123 Beauty Street, Atlanta, GA 30309",
    getInTouchTitle: "Get In Touch",
    getInTouchSubtitle: "Ready to start your hair journey? Contact us today!"
  },
  testimonials: {
    title: "What Our Clients Say",
    subtitle: "Real experiences from our satisfied customers"
  },
  reviews: {
    sectionTitle: "Customer Reviews",
    writeReviewButton: "Write a Review",
    noReviewsMessage: "No reviews yet. Be the first to share your experience!",
    formTitle: "Write Your Review",
    formSubtitle: "Share your experience with others",
    ratingLabel: "Rating",
    titleLabel: "Review Title",
    titlePlaceholder: "Summarize your experience",
    commentLabel: "Your Review",
    commentPlaceholder: "Tell us about your experience with this product or service...",
    submitButton: "Submit Review",
    submittingText: "Submitting...",
    successMessage: "Thank you for your review!",
    pendingApprovalMessage: "Your review is pending approval and will be visible soon.",
    editReviewTitle: "Edit Your Review",
    updateButton: "Update Review",
    updatingText: "Updating...",
    deleteConfirmMessage: "Are you sure you want to delete this review?",
    verifiedPurchaseLabel: "Verified Purchase",
    helpfulButton: "Helpful",
    reportButton: "Report",
    sortByLabel: "Sort by:",
    sortNewest: "Newest",
    sortOldest: "Oldest",
    sortHighestRated: "Highest Rated",
    sortLowestRated: "Lowest Rated",
    filterByRating: "Filter by Rating:",
    allRatings: "All Ratings",
    showMoreButton: "Show More Reviews",
    showLessButton: "Show Less"
  },
  paymentConfirmation: {
    pageTitle: "Payment Confirmation",
    pageSubtitle: "Upload your payment proof",
    pageDescription: "Please upload a screenshot or photo of your payment confirmation to complete your order.",
    uploadTitle: "Upload Payment Proof",
    uploadSubtitle: "Drag and drop your payment screenshot or browse to select",
    uploadInstructions: "Please upload a clear image of your payment confirmation, receipt, or transaction screenshot.",
    dragDropText: "Drag and drop your image here",
    browseFilesText: "or browse files",
    supportedFormats: "Supported formats: JPG, PNG, PDF (max 5MB)",
    maxFileSize: "Maximum file size: 5MB",
    orderReferenceLabel: "Order Reference",
    appointmentReferenceLabel: "Appointment Reference",
    amountLabel: "Payment Amount",
    paymentMethodLabel: "Payment Method",
    notesLabel: "Additional Notes",
    notesPlaceholder: "Any additional information about your payment...",
    submitButton: "Submit Payment Proof",
    submittingText: "Submitting...",
    successMessage: "Payment proof submitted successfully! We'll verify and process your order soon.",
    uploadingText: "Uploading image...",
    previewTitle: "Image Preview",
    removeImageButton: "Remove Image",
    retryUploadButton: "Retry Upload"
  },
  messages: {
    loading: "Loading...",
    error: "Something went wrong. Please try again.",
    notFound: "Page not found",
    comingSoon: "Coming soon!",
    cartShipping: "Free shipping on orders over $50"
  },
  business: {
    address: {
      street: "",
      city: "Indianapolis",
      state: "IN",
      zip: "",
      full: "Indianapolis, IN"
    },
    social: {
      instagram: "https://instagram.com/dammyspicybeauty",
      facebook: "https://facebook.com/dammyspicybeauty",
      twitter: "https://twitter.com/dammyspicybeauty"
    },
    hours: {
      monday: "9:00 AM - 6:00 PM",
      tuesday: "9:00 AM - 6:00 PM",
      wednesday: "9:00 AM - 6:00 PM",
      thursday: "9:00 AM - 6:00 PM",
      friday: "9:00 AM - 6:00 PM",
      saturday: "9:00 AM - 4:00 PM",
      sunday: "Closed"
    },
    name: "Dammyspicy Beauty",
    tagline: "Professional Hair Care Services",
    description: "Professional hair care and beauty treatments for natural hair, including microlocs and more. Based in Indianapolis, IN, specializing in helping women embrace their natural beauty with confidence.",
    phone: "(*************",
    email: "<EMAIL>"
  },
  theme: {
    colors: {
      primary: "#008000",
      secondary: "#f3d016",
      accent: "#006600",
      background: "#ffffff",
      text: "#000000",
      textSecondary: "#666666"
    },
    fonts: {
      primary: "Inter, system-ui, sans-serif",
      secondary: "Inter, system-ui, sans-serif",
      heading: "Inter, system-ui, sans-serif"
    }
  },
  site: {
    seo: {
      title: "Goldie Locs By Tina - Professional Loc Services",
      description: "Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.",
      keywords: "locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance"
    },
    features: {
      onlineBooking: true,
      ecommerce: true,
      loyaltyProgram: true,
      giftCards: true,
      reviews: true,
      blog: false
    }
  },
  legal: {
    privacyPolicy: {
      title: "Privacy Policy",
      content: `<h1>Privacy Policy</h1>
<p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

<h2>1. Information We Collect</h2>
<p>At MicroLocs, we collect information you provide directly to us, such as when you:</p>
<ul>
  <li>Create an account on our platform</li>
  <li>Book appointments for our services</li>
  <li>Purchase products from our shop</li>
  <li>Contact us for support or inquiries</li>
  <li>Subscribe to our newsletter</li>
</ul>

<h2>2. How We Use Your Information</h2>
<p>We use the information we collect to:</p>
<ul>
  <li>Provide, maintain, and improve our hair care services</li>
  <li>Process appointments and transactions</li>
  <li>Send you appointment reminders and service updates</li>
  <li>Communicate with you about our services and promotions</li>
  <li>Personalize your experience with our brand</li>
</ul>

<h2>3. Information Sharing and Disclosure</h2>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

<h2>4. Data Security</h2>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

<h2>5. Your Rights and Choices</h2>
<p>You have the right to access, update, or delete your personal information. Contact <NAME_EMAIL> to exercise these rights.</p>

<h2>6. Contact Us</h2>
<p>If you have questions about this Privacy Policy, please contact <NAME_EMAIL></p>`,
      lastUpdated: new Date()
    },
    termsOfService: {
      title: "Terms of Service",
      content: `<h1>Terms of Service</h1>
<p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

<h2>1. Acceptance of Terms</h2>
<p>By accessing and using MicroLocs services, you accept and agree to be bound by the terms and provisions of this agreement.</p>

<h2>2. Description of Services</h2>
<p>MicroLocs provides professional hair care services including micro loc installation, maintenance, natural hair care, and hair care product sales.</p>

<h2>3. Appointment Booking and Cancellation</h2>
<p>Appointments must be booked through our platform. Cancellations must be made at least 24 hours in advance to avoid fees.</p>

<h2>4. Payment Terms</h2>
<p>Payment is due at the time of service. We accept cash, credit cards, and digital payments.</p>

<h2>5. Liability</h2>
<p>MicroLocs is not liable for any damages resulting from the use of our services beyond the cost of the service provided.</p>

<h2>6. Contact Information</h2>
<p>For questions about these Terms of Service, please contact <NAME_EMAIL></p>`,
      lastUpdated: new Date()
    },
    copyrightText: "© 2024 MicroLocs. All rights reserved.",
    companyName: "MicroLocs"
  },
  legal: {
    privacyPolicy: {
      title: "Privacy Policy",
      content: `<h1>Privacy Policy</h1>
<p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

<h2>1. Information We Collect</h2>
<p>At MicroLocs, we collect information you provide directly to us, such as when you:</p>
<ul>
  <li>Create an account on our platform</li>
  <li>Book appointments for our services</li>
  <li>Purchase products from our shop</li>
  <li>Contact us for support or inquiries</li>
  <li>Subscribe to our newsletter</li>
</ul>

<h2>2. How We Use Your Information</h2>
<p>We use the information we collect to:</p>
<ul>
  <li>Provide, maintain, and improve our hair care services</li>
  <li>Process appointments and transactions</li>
  <li>Send you appointment reminders and service updates</li>
  <li>Communicate with you about our services and promotions</li>
  <li>Personalize your experience with our brand</li>
</ul>

<h2>3. Information Sharing and Disclosure</h2>
<p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information with:</p>
<ul>
  <li>Service providers who assist us in operating our business</li>
  <li>Legal authorities when required by law</li>
  <li>Business partners with your explicit consent</li>
</ul>

<h2>4. Data Security</h2>
<p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.</p>

<h2>5. Your Rights and Choices</h2>
<p>You have the right to:</p>
<ul>
  <li>Access and update your personal information</li>
  <li>Delete your account and associated data</li>
  <li>Opt out of marketing communications</li>
  <li>Request a copy of your data</li>
</ul>

<h2>6. Cookies and Tracking</h2>
<p>We use cookies and similar technologies to enhance your experience on our website, analyze usage patterns, and provide personalized content.</p>

<h2>7. Children's Privacy</h2>
<p>Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.</p>

<h2>8. Changes to This Policy</h2>
<p>We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date.</p>

<h2>9. Contact Us</h2>
<p>If you have questions about this Privacy Policy, please contact us at:</p>
<ul>
  <li>Email: <EMAIL></li>
  <li>Phone: (*************</li>
  <li>Address: 123 Beauty Street, Atlanta, GA 30309</li>
</ul>`,
      lastUpdated: new Date()
    },
    termsOfService: {
      title: "Terms of Service",
      content: `<h1>Terms of Service</h1>
<p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

<h2>1. Acceptance of Terms</h2>
<p>By accessing and using MicroLocs services, you accept and agree to be bound by the terms and provisions of this agreement. If you do not agree to these terms, please do not use our services.</p>

<h2>2. Description of Services</h2>
<p>MicroLocs provides professional hair care services including:</p>
<ul>
  <li>Micro loc installation and maintenance</li>
  <li>Traditional loc services</li>
  <li>Natural hair care and styling</li>
  <li>Hair care product sales</li>
  <li>Online appointment booking</li>
</ul>

<h2>3. Appointment Booking and Cancellation</h2>
<p>Appointments must be booked through our platform or by phone. Our cancellation policy includes:</p>
<ul>
  <li>Cancellations must be made at least 24 hours in advance</li>
  <li>Late cancellations (less than 24 hours) may incur a 50% service fee</li>
  <li>No-shows will be charged the full service amount</li>
  <li>Rescheduling is allowed up to 24 hours before your appointment</li>
</ul>

<h2>4. Payment Terms</h2>
<p>Payment policies:</p>
<ul>
  <li>Payment is due at the time of service</li>
  <li>We accept cash, credit cards, and digital payments</li>
  <li>Prices are subject to change with notice</li>
  <li>Gratuities are appreciated but not required</li>
</ul>

<h2>5. Service Expectations</h2>
<p>We strive to provide excellent service, but please note:</p>
<ul>
  <li>Hair care results may vary based on individual hair type and condition</li>
  <li>We recommend following aftercare instructions for best results</li>
  <li>Additional services may be recommended during your appointment</li>
</ul>

<h2>6. Product Sales and Returns</h2>
<p>For products purchased through our shop:</p>
<ul>
  <li>All sales are final unless products are defective</li>
  <li>Returns for defective products must be made within 7 days</li>
  <li>Custom or personalized products cannot be returned</li>
</ul>

<h2>7. Liability and Disclaimers</h2>
<p>MicroLocs is not liable for:</p>
<ul>
  <li>Allergic reactions to products (patch tests recommended)</li>
  <li>Hair damage due to pre-existing conditions</li>
  <li>Results that don't meet personal expectations</li>
  <li>Damages beyond the cost of the service provided</li>
</ul>

<h2>8. Intellectual Property</h2>
<p>All content on our website and marketing materials is protected by copyright and trademark laws. Unauthorized use is prohibited.</p>

<h2>9. Privacy</h2>
<p>Your privacy is important to us. Please review our Privacy Policy to understand how we collect and use your information.</p>

<h2>10. Modifications to Terms</h2>
<p>We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting on our website.</p>

<h2>11. Contact Information</h2>
<p>For questions about these Terms of Service, please contact us at:</p>
<ul>
  <li>Email: <EMAIL></li>
  <li>Phone: (*************</li>
  <li>Address: 123 Beauty Street, Atlanta, GA 30309</li>
</ul>`,
      lastUpdated: new Date()
    },
    copyrightText: "© 2024 MicroLocs. All rights reserved.",
    companyName: "MicroLocs"
  }
};

async function populateBrandingData() {
  try {
    // Connect to MongoDB
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocsbackend');
    console.log('✅ Connected to MongoDB');

    // Check if branding data already exists
    const existingBranding = await Branding.findOne();

    if (existingBranding) {
      console.log('📝 Updating existing branding data...');
      // Update existing document with new data
      Object.assign(existingBranding, brandingData);
      await existingBranding.save();
      console.log('✅ Branding data updated successfully!');
    } else {
      console.log('📝 Creating new branding data...');
      // Create new branding document
      const newBranding = new Branding(brandingData);
      await newBranding.save();
      console.log('✅ Branding data created successfully!');
    }

    console.log('🎉 Branding data population completed!');

    // Verify the data was saved correctly
    const savedBranding = await Branding.findOne();
    console.log('📊 Verification:');
    console.log(`   Site Name: ${savedBranding.global.siteName}`);
    console.log(`   Tagline: ${savedBranding.global.tagline}`);
    console.log(`   Business Name: ${savedBranding.business.name}`);
    console.log(`   Primary Color: ${savedBranding.theme.colors.primary}`);

  } catch (error) {
    console.error('❌ Error populating branding data:', error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  populateBrandingData();
}

module.exports = { populateBrandingData, brandingData };
