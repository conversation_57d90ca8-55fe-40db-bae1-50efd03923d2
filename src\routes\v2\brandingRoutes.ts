import express from 'express';
import { BrandingController } from '../../controllers';
import { authenticate, authorize } from '../../middleware/auth';

const router = express.Router();

// GET /api/v2/branding (public)
router.get('/', BrandingController.getBrandingContent);

// GET /api/v2/branding/complete (public) - aggregated endpoint
router.get('/complete', BrandingController.getCompleteBranding);

// PUT /api/v2/branding (admin only)
router.put('/', authenticate, authorize('admin'), BrandingController.updateBrandingContent);

// PUT /api/v2/branding/:section (admin only)
router.put('/:section', authenticate, authorize('admin'), BrandingController.updateBrandingSection);

// Individual branding endpoints (for backward compatibility)
router.get('/business-profile', BrandingController.getBusinessProfile);
router.get('/theme-settings', BrandingController.getThemeSettings);
router.get('/site-settings', BrandingController.getSiteSettings);

export default router;
