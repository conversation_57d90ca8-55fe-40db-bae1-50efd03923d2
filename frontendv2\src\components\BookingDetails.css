.booking-info {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 600px;
  overflow: hidden;
}

.info-header {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.info-header h3 {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.info-content {
  padding: 24px;
}

.info-field {
  margin-bottom: 24px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-transform: uppercase;
}

.text-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.2s;
}

.text-input:focus {
  border-color: #000;
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.text-input::placeholder {
  color: #999;
}

.input-hint {
  margin-top: 6px;
  font-size: 12px;
  color: #666;
}

.email-status {
  margin-top: 6px;
  font-size: 13px;
}

.status {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
}

.status.checking {
  color: #666;
}

.status.found {
  color: #52c41a;
}

.status.new {
  color: #1890ff;
}

.info-footer {
  padding: 24px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

.submit-button {
  width: 100%;
  padding: 14px 24px;
  background: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background: #333;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 4px;
}

/* Disclaimer Section */
.disclaimer-section {
  margin-top: 32px;
  padding: 24px;
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.disclaimer-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.disclaimer-content {
  font-size: 14px;
  line-height: 1.6;
  color: #444;
  white-space: pre-line;
}

.disclaimer-content p {
  margin-bottom: 16px;
}

.disclaimer-content p:last-child {
  margin-bottom: 0;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .booking-info {
    border-radius: 0;
    margin: 0;
    box-shadow: none;
  }

  .info-content {
    padding: 16px;
  }

  .info-field {
    margin-bottom: 20px;
  }

  .info-header,
  .info-footer {
    padding: 16px;
  }

  .text-input {
    font-size: 16px;
  }

  .info-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Add padding to prevent content from being hidden behind fixed footer */
  .booking-info {
    padding-bottom: 80px;
  }
}
