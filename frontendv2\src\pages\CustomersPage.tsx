import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { type User } from '../utils/api'
import { customerAPI, type Customer } from '../utils/customerAPI'
import { useToast } from '../contexts/ToastContext'

interface CustomersPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function CustomersPage({ currentUser, onLogout }: CustomersPageProps) {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'email' | 'totalSpent' | 'lastAppointment'>('name');
  const { showError, showInfo } = useToast();

  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      navigate('/');
      return;
    }

    fetchCustomers();
  }, [currentUser, navigate, sortBy]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await customerAPI.getCustomers({
        page: 1,
        limit: 100, // Get all customers for now
        sortBy: sortBy,
        sortOrder: 'desc'
      });

      if (response.success) {
        setCustomers(response.data.customers);
      } else {
        console.error('Failed to fetch customers');
        setCustomers([]);
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerView = async (customerId: string) => {
    try {
      const response = await customerAPI.getCustomer(customerId);
      if (response.success) {
        // Show customer details in a toast notification
        const customer = response.data;
        showInfo(`Customer Details: ${customer.firstName} ${customer.lastName} | Email: ${customer.email} | Phone: ${customer.phone || 'N/A'} | Total Appointments: ${customer.statistics.totalAppointments} | Total Spent: $${customer.statistics.totalSpent} | Verified: ${customer.isVerified ? 'Yes' : 'No'}`, 8000);
      }
    } catch (error) {
      console.error('Error fetching customer details:', error);
      showError('Failed to load customer details');
    }
  };

  const handleCustomerEdit = async (customerId: string) => {
    try {
      const response = await customerAPI.getCustomer(customerId);
      if (response.success) {
        const customer = response.data;
        const newPhone = prompt('Enter new phone number:', customer.phone || '');
        if (newPhone !== null) {
          const updateResponse = await customerAPI.updateCustomer(customerId, {
            phone: newPhone
          });
          if (updateResponse.success) {
            alert('Customer updated successfully!');
            fetchCustomers(); // Refresh the list
          } else {
            alert('Failed to update customer');
          }
        }
      }
    } catch (error) {
      console.error('Error updating customer:', error);
      alert('Failed to update customer');
    }
  };

  const filteredAndSortedCustomers = customers
    .filter(customer => 
      customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'totalSpent':
          return b.totalSpent - a.totalSpent;
        case 'lastAppointment':
          return new Date(b.lastAppointment || 0).getTime() - new Date(a.lastAppointment || 0).getTime();
        default:
          return 0;
      }
    });

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading customers...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Customers</h1>
          <div className="header-actions">
            <input
              type="text"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="sort-select"
            >
              <option value="name">Sort by Name</option>
              <option value="email">Sort by Email</option>
              <option value="totalSpent">Sort by Total Spent</option>
              <option value="lastAppointment">Sort by Last Appointment</option>
            </select>
          </div>
        </div>

        <div className="customers-stats">
          <div className="stat-card">
            <h3>Total Customers</h3>
            <p className="stat-number">{customers.length}</p>
          </div>
          <div className="stat-card">
            <h3>Active Customers</h3>
            <p className="stat-number">{customers.filter(c => c.status === 'active').length}</p>
          </div>
          <div className="stat-card">
            <h3>Total Revenue</h3>
            <p className="stat-number">${customers.reduce((sum, c) => sum + c.totalSpent, 0)}</p>
          </div>
          <div className="stat-card">
            <h3>Average Spent</h3>
            <p className="stat-number">
              ${Math.round(customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length)}
            </p>
          </div>
        </div>

        <div className="customers-list">
          {filteredAndSortedCustomers.length === 0 ? (
            <div className="empty-state">
              <p>No customers found matching your search.</p>
            </div>
          ) : (
            <div className="customers-table">
              <div className="table-header">
                <div>Name</div>
                <div>Email</div>
                <div>Phone</div>
                <div>Appointments</div>
                <div>Total Spent</div>
                <div>Last Visit</div>
                <div>Status</div>
                <div>Actions</div>
              </div>
              
              {filteredAndSortedCustomers.map(customer => (
                <div key={customer.id} className="table-row">
                  <div>{customer.firstName} {customer.lastName}</div>
                  <div>{customer.email}</div>
                  <div>{customer.phone || 'N/A'}</div>
                  <div>{customer.totalAppointments}</div>
                  <div>${customer.totalSpent}</div>
                  <div>{customer.lastAppointment || 'Never'}</div>
                  <div>
                    <span className={`status-badge ${customer.status}`}>
                      {customer.status}
                    </span>
                  </div>
                  <div className="table-actions">
                    <button 
                      className="btn-secondary"
                      onClick={() => handleCustomerView(customer.id)}
                    >
                      View
                    </button>
                    <button 
                      className="btn-secondary"
                      onClick={() => handleCustomerEdit(customer.id)}
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
