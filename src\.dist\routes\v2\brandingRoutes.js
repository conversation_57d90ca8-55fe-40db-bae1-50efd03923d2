"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const controllers_1 = require("../../controllers");
const auth_1 = require("../../middleware/auth");
const router = express_1.default.Router();
// GET /api/v2/branding (public)
router.get('/', controllers_1.BrandingController.getBrandingContent);
// GET /api/v2/branding/complete (public) - aggregated endpoint
router.get('/complete', controllers_1.BrandingController.getCompleteBranding);
// PUT /api/v2/branding (admin only)
router.put('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BrandingController.updateBrandingContent);
// PUT /api/v2/branding/:section (admin only)
router.put('/:section', auth_1.authenticate, (0, auth_1.authorize)('admin'), controllers_1.BrandingController.updateBrandingSection);
// Individual branding endpoints (for backward compatibility)
router.get('/business-profile', controllers_1.BrandingController.getBusinessProfile);
router.get('/theme-settings', controllers_1.BrandingController.getThemeSettings);
router.get('/site-settings', controllers_1.BrandingController.getSiteSettings);
exports.default = router;
