"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentController = void 0;
const services_1 = require("../services");
const response_1 = require("../utils/response");
class AppointmentController {
    static async getAvailability(req, res) {
        try {
            const { date, service } = req.query;
            if (!date) {
                (0, response_1.sendError)(res, 'Date parameter is required');
                return;
            }
            const availableSlots = await services_1.AppointmentService.getAvailableTimeSlots(date, service);
            (0, response_1.sendSuccess)(res, 'Available time slots retrieved successfully', {
                date,
                availableSlots
            });
        }
        catch (error) {
            console.error('Get availability error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async createAppointment(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { service, date, time, name, email, phone, message, url } = req.body;
            const appointment = await services_1.AppointmentService.createAppointment({
                user: req.user._id,
                service,
                date: new Date(date),
                time,
                customerInfo: {
                    name,
                    email,
                    phone
                },
                message,
                type: 'service'
            });
            (0, response_1.sendCreated)(res, 'Appointment created successfully', appointment);
        }
        catch (error) {
            console.error('Create appointment error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getUserAppointments(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { page, limit } = req.query;
            const pageNum = page ? parseInt(page) : undefined;
            const limitNum = limit ? parseInt(limit) : undefined;
            const result = await services_1.AppointmentService.getUserAppointments(req.user._id, pageNum, limitNum);
            (0, response_1.sendSuccess)(res, 'User appointments retrieved successfully', result);
        }
        catch (error) {
            console.error('Get user appointments error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateAppointment(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            const updateData = req.body;
            const appointment = await services_1.AppointmentService.updateAppointment(id, req.user._id, updateData);
            (0, response_1.sendSuccess)(res, 'Appointment updated successfully', appointment);
        }
        catch (error) {
            console.error('Update appointment error:', error);
            if (error.message === 'Appointment not found') {
                (0, response_1.sendNotFound)(res, 'Appointment not found');
            }
            else {
                (0, response_1.sendError)(res, error.message);
            }
        }
    }
    static async cancelAppointment(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            const { id } = req.params;
            await services_1.AppointmentService.cancelAppointment(id, req.user._id);
            (0, response_1.sendSuccess)(res, 'Appointment cancelled successfully');
        }
        catch (error) {
            console.error('Cancel appointment error:', error);
            if (error.message === 'Appointment not found') {
                (0, response_1.sendNotFound)(res, 'Appointment not found');
            }
            else {
                (0, response_1.sendError)(res, error.message);
            }
        }
    }
    static async getAllAppointments(req, res) {
        try {
            const query = {
                date: req.query.date,
                status: req.query.status,
                service: req.query.service
            };
            const appointments = await services_1.AppointmentService.getAppointments(query);
            (0, response_1.sendSuccess)(res, 'Appointments retrieved successfully', appointments);
        }
        catch (error) {
            console.error('Get all appointments error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.AppointmentController = AppointmentController;
