import { useState, useEffect } from 'react';
import { serviceAPI } from '../../utils/serviceAPI';

interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: string[];
  onCategoriesChange: () => void;
  onError?: (message: string) => void;
  onSuccess?: (message: string) => void;
}

export default function CategoryModal({
  isOpen,
  onClose,
  categories,
  onCategoriesChange,
  onError,
  onSuccess
}: CategoryModalProps) {
  const [newCategoryName, setNewCategoryName] = useState('');
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setNewCategoryName('');
      setEditingCategory(null);
      setEditingName('');
      setError(null);
    }
  }, [isOpen]);

  // Handle adding new category
  const handleAddCategory = async () => {
    const trimmedName = newCategoryName.trim();
    
    if (!trimmedName) {
      setError('Category name is required');
      return;
    }

    if (trimmedName.length > 100) {
      setError('Category name must be 100 characters or less');
      return;
    }

    if (categories.some(cat => cat.toLowerCase() === trimmedName.toLowerCase())) {
      setError('Category already exists');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await serviceAPI.createCategory(trimmedName);
      if (response.success) {
        onSuccess?.(`Category "${trimmedName}" will be available when you create a service with this category`);
        setNewCategoryName('');
        onCategoriesChange();
      } else {
        throw new Error('Failed to create category');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add category';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing category
  const handleEditCategory = (category: string) => {
    setEditingCategory(category);
    setEditingName(category);
    setError(null);
  };

  // Handle saving edited category
  const handleSaveEdit = async () => {
    const trimmedName = editingName.trim();
    
    if (!trimmedName) {
      setError('Category name is required');
      return;
    }

    if (trimmedName.length > 100) {
      setError('Category name must be 100 characters or less');
      return;
    }

    if (trimmedName !== editingCategory && 
        categories.some(cat => cat.toLowerCase() === trimmedName.toLowerCase())) {
      setError('Category already exists');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await serviceAPI.updateCategory(editingCategory!, trimmedName);
      if (response.success) {
        onSuccess?.(`Category renamed from "${editingCategory}" to "${trimmedName}"`);
        setEditingCategory(null);
        setEditingName('');
        onCategoriesChange();
      } else {
        throw new Error('Failed to update category');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update category';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting category
  const handleDeleteCategory = async (category: string) => {
    if (!confirm(`Are you sure you want to delete the "${category}" category? This action cannot be undone and will affect all services in this category.`)) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await serviceAPI.deleteCategory(category);
      if (response.success) {
        onSuccess?.(`Category "${category}" deleted successfully`);
        onCategoriesChange();
      } else {
        throw new Error('Failed to delete category');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete category';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingCategory(null);
    setEditingName('');
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container modal-md">
        {/* Header */}
        <div className="modal-header">
          <h2 className="modal-title">Manage Categories</h2>
          <button
            onClick={onClose}
            className="modal-close-button"
            disabled={isSubmitting}
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="modal-content">
          {/* Error Display */}
          {error && (
            <div className="error-message mb-4">
              <span>{error}</span>
              <button 
                onClick={() => setError(null)}
                className="error-close"
              >
                ×
              </button>
            </div>
          )}

          {/* Add New Category */}
          <div className="form-section">
            <h4>Add New Category</h4>
            <div className="flex gap-2">
              <input
                type="text"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder="Enter category name"
                className="form-input flex-1"
                disabled={isSubmitting}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddCategory();
                  }
                }}
              />
              <button
                onClick={handleAddCategory}
                disabled={isSubmitting || !newCategoryName.trim()}
                className="btn btn-primary"
              >
                Add
              </button>
            </div>
          </div>

          {/* Existing Categories */}
          <div className="form-section">
            <h4>Existing Categories ({categories.length})</h4>
            
            {categories.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No categories found. Categories are created automatically when you add services.
              </p>
            ) : (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {categories.map((category) => (
                  <div
                    key={category}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    {editingCategory === category ? (
                      <div className="flex items-center gap-2 flex-1">
                        <input
                          type="text"
                          value={editingName}
                          onChange={(e) => setEditingName(e.target.value)}
                          className="form-input flex-1"
                          disabled={isSubmitting}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveEdit();
                            } else if (e.key === 'Escape') {
                              handleCancelEdit();
                            }
                          }}
                          autoFocus
                        />
                        <button
                          onClick={handleSaveEdit}
                          disabled={isSubmitting || !editingName.trim()}
                          className="btn btn-sm btn-primary"
                        >
                          Save
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          disabled={isSubmitting}
                          className="btn btn-sm btn-outline"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <>
                        <span className="font-medium text-gray-900">{category}</span>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEditCategory(category)}
                            disabled={isSubmitting}
                            className="btn btn-sm btn-outline"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteCategory(category)}
                            disabled={isSubmitting}
                            className="btn btn-sm btn-danger"
                          >
                            Delete
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-900 mb-2">Category Management</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Categories are automatically created when you add services</li>
              <li>• Editing a category will update all services in that category</li>
              <li>• Deleting a category will affect all services using it</li>
              <li>• You can also create categories directly when adding services</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="form-actions">
            <button
              onClick={onClose}
              className="btn btn-outline"
              disabled={isSubmitting}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
