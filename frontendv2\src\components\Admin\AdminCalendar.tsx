import { useState, useEffect } from 'react';
import Calendar from 'react-calendar';
import { format } from 'date-fns';
import { availability } from '../../utils/api';
import type { TimeSlot } from '../../utils/api';
import 'react-calendar/dist/Calendar.css';

export default function AdminCalendar() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isFullDayUnavailable, setIsFullDayUnavailable] = useState(false);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch availability for selected date
  useEffect(() => {
    const fetchAvailability = async () => {
      try {
        setLoading(true);
        const response = await availability.getTimeSlots(format(selectedDate, 'yyyy-MM-dd'));
        if (response.success) {
          setTimeSlots(response.timeSlots);
          setIsFullDayUnavailable(response.isFullDayUnavailable || false);
          setReason(response.reason || '');
        }
      } catch (err) {
        setError('Error fetching availability');
        console.error('Error fetching availability:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAvailability();
  }, [selectedDate]);

  // Handle time slot toggle
  const handleTimeSlotToggle = (index: number) => {
    const newTimeSlots = [...timeSlots];
    newTimeSlots[index] = {
      ...newTimeSlots[index],
      isAvailable: !newTimeSlots[index].isAvailable
    };
    setTimeSlots(newTimeSlots);
  };

  // Handle full day unavailability toggle
  const handleFullDayToggle = () => {
    setIsFullDayUnavailable(!isFullDayUnavailable);
  };

  // Save availability changes
  const handleSave = async () => {
    try {
      setLoading(true);
      const response = await availability.setAvailability({
        date: format(selectedDate, 'yyyy-MM-dd'),
        timeSlots,
        isFullDayUnavailable,
        reason
      });

      if (response.success) {
        setError(null);
        // Show success message
      }
    } catch (err) {
      setError('Error saving availability');
      console.error('Error saving availability:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-calendar">
      <h2>Manage Availability</h2>
      
      <div className="calendar-container">
        <div className="calendar-section">
          <Calendar
            onChange={(value: any) => {
              if (value instanceof Date) {
                setSelectedDate(value);
              }
            }}
            value={selectedDate}
            minDate={new Date()}
          />
        </div>

        <div className="availability-section">
          <h3>Availability for {format(selectedDate, 'MMMM d, yyyy')}</h3>
          
          {loading ? (
            <div>Loading...</div>
          ) : (
            <>
              <div className="full-day-control">
                <label>
                  <input
                    type="checkbox"
                    checked={isFullDayUnavailable}
                    onChange={handleFullDayToggle}
                  />
                  Mark Full Day as Unavailable
                </label>
              </div>

              {isFullDayUnavailable && (
                <div className="reason-input">
                  <label>
                    Reason (optional):
                    <input
                      type="text"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder="e.g., Holiday, Personal Day, etc."
                    />
                  </label>
                </div>
              )}

              {!isFullDayUnavailable && (
                <div className="time-slots">
                  {timeSlots.map((slot, index) => (
                    <div key={slot.time} className="time-slot">
                      <label>
                        <input
                          type="checkbox"
                          checked={slot.isAvailable}
                          onChange={() => handleTimeSlotToggle(index)}
                        />
                        {slot.time}
                      </label>
                    </div>
                  ))}
                </div>
              )}

              <button
                onClick={handleSave}
                className="save-button"
                disabled={loading}
              >
                Save Changes
              </button>

              {error && <div className="error-message">{error}</div>}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
