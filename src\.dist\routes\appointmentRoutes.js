"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// Note: Availability checking has been moved to /api/v2/availability endpoints
// This endpoint is deprecated and will be removed in a future version
// POST /api/appointments
router.post('/', auth_1.authenticate, (0, validation_1.validate)(validation_2.createAppointmentValidation), controllers_1.AppointmentController.createAppointment);
// GET /api/appointments/user
router.get('/user', auth_1.authenticate, (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AppointmentController.getUserAppointments);
// PUT /api/appointments/:id
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)(validation_2.updateAppointmentValidation), controllers_1.AppointmentController.updateAppointment);
// DELETE /api/appointments/:id
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), controllers_1.AppointmentController.cancelAppointment);
// Admin routes
// GET /api/appointments (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), controllers_1.AppointmentController.getAllAppointments);
exports.default = router;
