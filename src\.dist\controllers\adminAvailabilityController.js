"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminAvailabilityController = void 0;
const adminAvailability_1 = require("../models/adminAvailability");
const response_1 = require("../utils/response");
// Utility function to generate default time slots
function generateDefaultTimeSlots(businessHours, slotDuration = 30) {
    const slots = [];
    const [startHour, startMinute] = businessHours.start.split(':').map(Number);
    const [endHour, endMinute] = businessHours.end.split(':').map(Number);
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;
    for (let time = startTime; time < endTime; time += slotDuration) {
        const hour = Math.floor(time / 60);
        const minute = time % 60;
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push({
            time: timeString,
            isAvailable: true
        });
    }
    return slots;
}
exports.adminAvailabilityController = {
    // Get global availability settings
    async getGlobalSettings(req, res) {
        try {
            let settings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            if (!settings) {
                // Create default settings if none exist
                settings = await adminAvailability_1.GlobalAvailabilitySettings.create({
                    defaultTimeSlots: generateDefaultTimeSlots({ start: '09:00', end: '17:00' }),
                    businessHours: { start: '09:00', end: '17:00' },
                    workingDays: [1, 2, 3, 4, 5],
                    slotDuration: 30,
                    isGloballyUnavailable: false
                });
            }
            return (0, response_1.sendSuccess)(res, 'Global settings retrieved successfully', settings);
        }
        catch (error) {
            console.error('Error fetching global settings:', error);
            return (0, response_1.sendError)(res, 'Error fetching global settings');
        }
    },
    // Update global availability settings
    async updateGlobalSettings(req, res) {
        try {
            const { businessHours, workingDays, slotDuration, isGloballyUnavailable, globalUnavailabilityReason } = req.body;
            let settings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            if (!settings) {
                settings = new adminAvailability_1.GlobalAvailabilitySettings();
            }
            if (businessHours) {
                settings.businessHours = businessHours;
                // Regenerate default time slots when business hours change
                settings.defaultTimeSlots = generateDefaultTimeSlots(businessHours, slotDuration || settings.slotDuration);
            }
            if (workingDays)
                settings.workingDays = workingDays;
            if (slotDuration) {
                settings.slotDuration = slotDuration;
                // Regenerate default time slots when duration changes
                settings.defaultTimeSlots = generateDefaultTimeSlots(settings.businessHours, slotDuration);
            }
            if (isGloballyUnavailable !== undefined)
                settings.isGloballyUnavailable = isGloballyUnavailable;
            if (globalUnavailabilityReason !== undefined)
                settings.globalUnavailabilityReason = globalUnavailabilityReason;
            await settings.save();
            return (0, response_1.sendSuccess)(res, 'Global settings updated successfully', settings);
        }
        catch (error) {
            console.error('Error updating global settings:', error);
            return (0, response_1.sendError)(res, 'Error updating global settings');
        }
    },
    // Get availability for a date range
    async getAvailability(req, res) {
        try {
            const { startDate, endDate } = req.query;
            if (!startDate || !endDate) {
                return (0, response_1.sendError)(res, 'Start date and end date are required');
            }
            const start = new Date(startDate);
            const end = new Date(endDate);
            // Get global settings
            const globalSettings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            // Get specific availability overrides
            const availability = await adminAvailability_1.AdminAvailability.find({
                date: { $gte: start, $lte: end }
            }).sort({ date: 1 });
            // Generate complete availability data for the date range
            const dateRange = [];
            const currentDate = new Date(start);
            while (currentDate <= end) {
                const dateString = currentDate.toISOString().split('T')[0];
                const dayOfWeek = currentDate.getDay();
                // Check if this date has specific availability settings
                const specificAvailability = availability.find(a => a.date.toISOString().split('T')[0] === dateString);
                let dayAvailability;
                if (specificAvailability) {
                    // Use specific settings
                    dayAvailability = {
                        date: dateString,
                        isFullDayUnavailable: specificAvailability.isFullDayUnavailable,
                        timeSlots: specificAvailability.timeSlots,
                        reason: specificAvailability.reason,
                        isWorkingDay: globalSettings?.workingDays.includes(dayOfWeek) ?? true,
                        hasCustomSettings: true
                    };
                }
                else {
                    // Use default settings
                    const isWorkingDay = globalSettings?.workingDays.includes(dayOfWeek) ?? true;
                    dayAvailability = {
                        date: dateString,
                        isFullDayUnavailable: !isWorkingDay || (globalSettings?.isGloballyUnavailable ?? false),
                        timeSlots: isWorkingDay ? (globalSettings?.defaultTimeSlots ?? []) : [],
                        reason: !isWorkingDay ? 'Non-working day' : globalSettings?.globalUnavailabilityReason,
                        isWorkingDay,
                        hasCustomSettings: false
                    };
                }
                dateRange.push(dayAvailability);
                currentDate.setDate(currentDate.getDate() + 1);
            }
            return (0, response_1.sendSuccess)(res, 'Availability retrieved successfully', {
                availability: dateRange,
                globalSettings: globalSettings ? {
                    businessHours: globalSettings.businessHours,
                    workingDays: globalSettings.workingDays,
                    slotDuration: globalSettings.slotDuration,
                    isGloballyUnavailable: globalSettings.isGloballyUnavailable,
                    globalUnavailabilityReason: globalSettings.globalUnavailabilityReason
                } : null
            });
        }
        catch (error) {
            console.error('Error fetching availability:', error);
            return (0, response_1.sendError)(res, 'Error fetching availability');
        }
    },
    // Set availability for a specific date
    async setAvailability(req, res) {
        try {
            const { date, timeSlots, isFullDayUnavailable, reason } = req.body;
            if (!date) {
                return (0, response_1.sendError)(res, 'Date is required');
            }
            // Validate time slots if provided
            if (timeSlots && !Array.isArray(timeSlots)) {
                return (0, response_1.sendError)(res, 'Time slots must be an array');
            }
            // Convert date string to Date object
            const availabilityDate = new Date(date);
            // Validate date is not in the past
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (availabilityDate < today) {
                return (0, response_1.sendError)(res, 'Cannot set availability for past dates');
            }
            // Validate and adjust time slots based on isFullDayUnavailable
            let validatedTimeSlots = timeSlots;
            if (isFullDayUnavailable) {
                // If full day is unavailable, ensure ALL time slots are set to false
                if (validatedTimeSlots && validatedTimeSlots.length > 0) {
                    validatedTimeSlots = validatedTimeSlots.map((slot) => ({
                        ...slot,
                        isAvailable: false
                    }));
                }
            }
            else {
                // If not full day unavailable, validate individual time slot selections
                if (validatedTimeSlots && validatedTimeSlots.length > 0) {
                    // Ensure each time slot has the isAvailable property
                    validatedTimeSlots = validatedTimeSlots.map((slot) => ({
                        time: slot.time,
                        isAvailable: slot.isAvailable !== undefined ? slot.isAvailable : true
                    }));
                }
            }
            // Update or create availability
            const availability = await adminAvailability_1.AdminAvailability.findOneAndUpdate({ date: availabilityDate }, {
                date: availabilityDate,
                timeSlots: validatedTimeSlots,
                isFullDayUnavailable,
                reason,
                createdBy: req.user?._id
            }, { upsert: true, new: true });
            return (0, response_1.sendSuccess)(res, 'Availability set successfully', availability);
        }
        catch (error) {
            console.error('Error setting availability:', error);
            return (0, response_1.sendError)(res, 'Error setting availability');
        }
    },
    // Set availability for multiple dates (bulk operation)
    async setBulkAvailability(req, res) {
        try {
            const { dates, timeSlots, isFullDayUnavailable, reason } = req.body;
            if (!dates || !Array.isArray(dates) || dates.length === 0) {
                return (0, response_1.sendError)(res, 'Dates array is required');
            }
            // Validate time slots if provided
            if (timeSlots && !Array.isArray(timeSlots)) {
                return (0, response_1.sendError)(res, 'Time slots must be an array');
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            // Validate and adjust time slots based on isFullDayUnavailable
            let validatedTimeSlots = timeSlots;
            if (isFullDayUnavailable) {
                // If full day is unavailable, ensure ALL time slots are set to false
                if (validatedTimeSlots && validatedTimeSlots.length > 0) {
                    validatedTimeSlots = validatedTimeSlots.map((slot) => ({
                        ...slot,
                        isAvailable: false
                    }));
                }
            }
            else {
                // If not full day unavailable, validate individual time slot selections
                if (validatedTimeSlots && validatedTimeSlots.length > 0) {
                    // Ensure each time slot has the isAvailable property
                    validatedTimeSlots = validatedTimeSlots.map((slot) => ({
                        time: slot.time,
                        isAvailable: slot.isAvailable !== undefined ? slot.isAvailable : true
                    }));
                }
            }
            const operations = dates
                .map(dateString => {
                const availabilityDate = new Date(dateString);
                // Skip past dates
                if (availabilityDate < today) {
                    return null;
                }
                return {
                    updateOne: {
                        filter: { date: availabilityDate },
                        update: {
                            date: availabilityDate,
                            timeSlots: validatedTimeSlots,
                            isFullDayUnavailable,
                            reason,
                            createdBy: req.user?._id
                        },
                        upsert: true
                    }
                };
            })
                .filter((op) => op !== null);
            if (operations.length === 0) {
                return (0, response_1.sendError)(res, 'No valid future dates provided');
            }
            const result = await adminAvailability_1.AdminAvailability.bulkWrite(operations);
            return (0, response_1.sendSuccess)(res, 'Bulk availability set successfully', {
                modifiedCount: result.modifiedCount,
                upsertedCount: result.upsertedCount,
                totalProcessed: operations.length
            });
        }
        catch (error) {
            console.error('Error setting bulk availability:', error);
            return (0, response_1.sendError)(res, 'Error setting bulk availability');
        }
    },
    // Delete availability for a specific date
    async deleteAvailability(req, res) {
        try {
            const { date } = req.params;
            if (!date) {
                return (0, response_1.sendError)(res, 'Date is required');
            }
            await adminAvailability_1.AdminAvailability.deleteOne({ date: new Date(date) });
            return (0, response_1.sendSuccess)(res, 'Availability deleted successfully', { date });
        }
        catch (error) {
            console.error('Error deleting availability:', error);
            return (0, response_1.sendError)(res, 'Error deleting availability');
        }
    },
    // Delete availability for multiple dates (bulk operation)
    async deleteBulkAvailability(req, res) {
        try {
            const { dates } = req.body;
            if (!dates || !Array.isArray(dates) || dates.length === 0) {
                return (0, response_1.sendError)(res, 'Dates array is required');
            }
            const dateObjects = dates.map(dateString => new Date(dateString));
            const result = await adminAvailability_1.AdminAvailability.deleteMany({
                date: { $in: dateObjects }
            });
            return (0, response_1.sendSuccess)(res, 'Bulk availability deleted successfully', {
                deletedCount: result.deletedCount,
                totalRequested: dates.length
            });
        }
        catch (error) {
            console.error('Error deleting bulk availability:', error);
            return (0, response_1.sendError)(res, 'Error deleting bulk availability');
        }
    },
    // Get available time slots for a specific date (PUBLIC API - only returns available slots)
    async getAvailableTimeSlots(req, res) {
        try {
            const { date } = req.params;
            if (!date) {
                return (0, response_1.sendError)(res, 'Date is required');
            }
            // Parse date properly to avoid timezone issues
            const requestDate = new Date(date + 'T00:00:00.000Z');
            const dayOfWeek = requestDate.getDay();
            // Debug logging removed for production
            // Get global settings
            const globalSettings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            // Check if globally unavailable
            if (globalSettings?.isGloballyUnavailable) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: globalSettings.globalUnavailabilityReason || 'Globally unavailable'
                });
            }
            // Check if it's a working day
            const isWorkingDay = globalSettings?.workingDays.includes(dayOfWeek) ?? true;
            if (!isWorkingDay) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: 'Non-working day'
                });
            }
            // Create date range for the selected day to handle timezone issues
            const startOfDay = new Date(requestDate);
            startOfDay.setUTCHours(0, 0, 0, 0);
            const endOfDay = new Date(requestDate);
            endOfDay.setUTCHours(23, 59, 59, 999);
            const availability = await adminAvailability_1.AdminAvailability.findOne({
                date: {
                    $gte: startOfDay,
                    $lte: endOfDay
                }
            });
            // If no specific availability set, use default time slots
            if (!availability) {
                const defaultTimeSlots = globalSettings?.defaultTimeSlots || generateDefaultTimeSlots({ start: '09:00', end: '17:00' });
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', { timeSlots: defaultTimeSlots });
            }
            // If full day is unavailable, return empty array
            if (availability.isFullDayUnavailable) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: availability.reason || 'Day unavailable'
                });
            }
            // Filter out unavailable time slots for public API
            const availableTimeSlots = availability.timeSlots.filter(slot => slot.isAvailable);
            // If no time slots are available, return empty with reason
            if (availableTimeSlots.length === 0) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: 'No available time slots'
                });
            }
            return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', { timeSlots: availableTimeSlots });
        }
        catch (error) {
            console.error('Error fetching time slots:', error);
            return (0, response_1.sendError)(res, 'Error fetching time slots');
        }
    },
    // Get ALL time slots for a specific date (ADMIN API - returns all slots with availability status)
    async getAdminTimeSlots(req, res) {
        try {
            const { date } = req.params;
            if (!date) {
                return (0, response_1.sendError)(res, 'Date is required');
            }
            // Parse date properly to avoid timezone issues
            const requestDate = new Date(date + 'T00:00:00.000Z');
            const dayOfWeek = requestDate.getDay();
            // Get global settings
            const globalSettings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            // Check if globally unavailable
            if (globalSettings?.isGloballyUnavailable) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: globalSettings.globalUnavailabilityReason || 'Globally unavailable',
                    isFullDayUnavailable: true
                });
            }
            // Check if it's a working day
            const isWorkingDay = globalSettings?.workingDays.includes(dayOfWeek) ?? true;
            if (!isWorkingDay) {
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: [],
                    reason: 'Non-working day',
                    isFullDayUnavailable: true
                });
            }
            // Create date range for the selected day to handle timezone issues
            const startOfDay = new Date(requestDate);
            startOfDay.setUTCHours(0, 0, 0, 0);
            const endOfDay = new Date(requestDate);
            endOfDay.setUTCHours(23, 59, 59, 999);
            const availability = await adminAvailability_1.AdminAvailability.findOne({
                date: {
                    $gte: startOfDay,
                    $lte: endOfDay
                }
            });
            // If no specific availability set, use default time slots
            if (!availability) {
                const defaultTimeSlots = globalSettings?.defaultTimeSlots || generateDefaultTimeSlots({ start: '09:00', end: '17:00' });
                return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                    timeSlots: defaultTimeSlots,
                    isFullDayUnavailable: false
                });
            }
            // Return all time slots with their availability status for admin
            return (0, response_1.sendSuccess)(res, 'Time slots retrieved successfully', {
                timeSlots: availability.timeSlots,
                isFullDayUnavailable: availability.isFullDayUnavailable,
                reason: availability.reason
            });
        }
        catch (error) {
            console.error('Error fetching admin time slots:', error);
            return (0, response_1.sendError)(res, 'Error fetching admin time slots');
        }
    },
    // Check if a specific time slot is available
    async checkTimeSlotAvailability(req, res) {
        try {
            const { date, time } = req.query;
            if (!date || !time) {
                return (0, response_1.sendError)(res, 'Date and time are required');
            }
            const requestDate = new Date(date);
            const dayOfWeek = requestDate.getDay();
            // Get global settings
            const globalSettings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
            // Check if globally unavailable
            if (globalSettings?.isGloballyUnavailable) {
                return (0, response_1.sendSuccess)(res, 'Time slot availability checked', {
                    isAvailable: false,
                    reason: globalSettings.globalUnavailabilityReason || 'Globally unavailable'
                });
            }
            // Check if it's a working day
            const isWorkingDay = globalSettings?.workingDays.includes(dayOfWeek) ?? true;
            if (!isWorkingDay) {
                return (0, response_1.sendSuccess)(res, 'Time slot availability checked', {
                    isAvailable: false,
                    reason: 'Non-working day'
                });
            }
            const availability = await adminAvailability_1.AdminAvailability.findOne({
                date: requestDate
            });
            // If no specific availability set, check against default time slots
            if (!availability) {
                const defaultTimeSlots = globalSettings?.defaultTimeSlots || generateDefaultTimeSlots({ start: '09:00', end: '17:00' });
                const timeSlot = defaultTimeSlots.find(slot => slot.time === time);
                return (0, response_1.sendSuccess)(res, 'Time slot availability checked', {
                    isAvailable: timeSlot?.isAvailable ?? false,
                    reason: timeSlot?.reason
                });
            }
            // If full day is unavailable
            if (availability.isFullDayUnavailable) {
                return (0, response_1.sendSuccess)(res, 'Time slot availability checked', {
                    isAvailable: false,
                    reason: availability.reason || 'Day unavailable'
                });
            }
            // Check specific time slot
            const timeSlot = availability.timeSlots.find(slot => slot.time === time);
            return (0, response_1.sendSuccess)(res, 'Time slot availability checked', {
                isAvailable: timeSlot?.isAvailable ?? false,
                reason: timeSlot?.reason
            });
        }
        catch (error) {
            console.error('Error checking time slot availability:', error);
            return (0, response_1.sendError)(res, 'Error checking time slot availability');
        }
    }
};
exports.default = exports.adminAvailabilityController;
