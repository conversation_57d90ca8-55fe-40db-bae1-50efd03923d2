"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// Note: Registration/signup functionality has been removed
// Users are created through admin interface or appointment booking
// POST /api/auth/login
router.post('/login', (0, validation_1.validate)(validation_2.loginValidation), controllers_1.AuthController.login);
// POST /api/auth/forgot-password
router.post('/forgot-password', (0, validation_1.validate)(validation_2.forgotPasswordValidation), controllers_1.AuthController.forgotPassword);
// POST /api/auth/reset-password
router.post('/reset-password', controllers_1.AuthController.resetPassword);
// GET /api/auth/verify
router.get('/verify', auth_1.authenticate, controllers_1.AuthController.verify);
// POST /api/auth/logout
router.post('/logout', auth_1.authenticate, controllers_1.AuthController.logout);
exports.default = router;
