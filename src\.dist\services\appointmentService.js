"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentService = void 0;
const models_1 = require("../models");
const adminAvailability_1 = require("../models/adminAvailability");
const emailService_1 = require("./emailService");
class AppointmentService {
    static async getAvailableTimeSlots(date, serviceId) {
        const appointmentDate = new Date(date);
        const dayOfWeek = appointmentDate.getDay();
        // First check admin availability
        const adminAvailability = await this.checkAdminAvailability(appointmentDate);
        if (!adminAvailability.isAvailable) {
            return []; // No slots available if admin is not available
        }
        // Get admin's available time slots for this date
        const adminTimeSlots = adminAvailability.timeSlots || [];
        const adminAvailableTimes = adminTimeSlots
            .filter(slot => slot.isAvailable)
            .map(slot => slot.time);
        if (adminAvailableTimes.length === 0) {
            return []; // No admin time slots available
        }
        // Get all booked appointments for the date
        const bookedAppointments = await models_1.Appointment.find({
            date: appointmentDate,
            status: { $in: ['pending', 'confirmed'] }
        }).select('time');
        const bookedTimes = bookedAppointments.map(apt => apt.time);
        // Filter out booked times from admin available times
        const availableSlots = adminAvailableTimes.filter(slot => !bookedTimes.includes(slot));
        return availableSlots;
    }
    static async checkAdminAvailability(date) {
        const dayOfWeek = date.getDay();
        // Get global settings
        const globalSettings = await adminAvailability_1.GlobalAvailabilitySettings.findOne();
        // Check if globally unavailable
        if (globalSettings?.isGloballyUnavailable) {
            return {
                isAvailable: false,
                reason: globalSettings.globalUnavailabilityReason || 'Globally unavailable'
            };
        }
        // Check if it's a working day
        const isWorkingDay = globalSettings?.workingDays.includes(dayOfWeek) ?? true;
        if (!isWorkingDay) {
            return {
                isAvailable: false,
                reason: 'Non-working day'
            };
        }
        // Check specific availability for this date
        const availability = await adminAvailability_1.AdminAvailability.findOne({ date });
        if (!availability) {
            // Use default time slots from global settings
            const defaultTimeSlots = globalSettings?.defaultTimeSlots || this.generateDefaultTimeSlots();
            return {
                isAvailable: true,
                timeSlots: defaultTimeSlots
            };
        }
        // If full day is unavailable
        if (availability.isFullDayUnavailable) {
            return {
                isAvailable: false,
                reason: availability.reason || 'Day unavailable'
            };
        }
        return {
            isAvailable: true,
            timeSlots: availability.timeSlots
        };
    }
    static generateDefaultTimeSlots() {
        const slots = [];
        for (let hour = 9; hour < 17; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
                const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                slots.push({
                    time: timeSlot,
                    isAvailable: true
                });
            }
        }
        return slots;
    }
    static async createAppointment(appointmentData) {
        // First check admin availability
        const adminAvailability = await this.checkAdminAvailability(appointmentData.date);
        if (!adminAvailability.isAvailable) {
            throw new Error(`Appointment not available: ${adminAvailability.reason}`);
        }
        // Check if the specific time slot is available from admin
        const adminTimeSlot = adminAvailability.timeSlots?.find(slot => slot.time === appointmentData.time);
        if (!adminTimeSlot || !adminTimeSlot.isAvailable) {
            throw new Error(`Time slot ${appointmentData.time} is not available: ${adminTimeSlot?.reason || 'Admin unavailable'}`);
        }
        // Check if the time slot is already booked
        const existingAppointment = await models_1.Appointment.findOne({
            date: appointmentData.date,
            time: appointmentData.time,
            status: { $in: ['pending', 'confirmed'] }
        });
        if (existingAppointment) {
            throw new Error('This time slot is already booked');
        }
        // Verify service exists
        const service = await models_1.Service.findById(appointmentData.service);
        if (!service) {
            throw new Error('Service not found');
        }
        // Create appointment
        const appointment = await models_1.Appointment.create(appointmentData);
        await appointment.populate('service', 'name duration price');
        // Send confirmation email asynchronously
        try {
            // Get user details for email
            const user = await models_1.User.findById(appointmentData.user);
            if (user) {
                emailService_1.emailService.sendAppointmentConfirmation(user, {
                    ...appointment.toObject(),
                    service: service.name,
                    duration: service.duration
                }).catch(err => console.error('Failed to send appointment confirmation email:', err));
            }
        }
        catch (error) {
            console.error('Email service error:', error);
            // Don't fail the appointment creation if email fails
        }
        return appointment;
    }
    static async getUserAppointments(userId, page, limit) {
        if (page && limit) {
            // Return paginated results
            const skip = (page - 1) * limit;
            const [appointments, total] = await Promise.all([
                models_1.Appointment.find({ user: userId })
                    .populate('service', 'name duration price category')
                    .sort({ date: -1, time: -1 }) // Most recent first for paginated results
                    .skip(skip)
                    .limit(limit),
                models_1.Appointment.countDocuments({ user: userId })
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                appointments,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalItems: total,
                    itemsPerPage: limit,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                }
            };
        }
        else {
            // Return all appointments (for backward compatibility)
            const appointments = await models_1.Appointment.find({ user: userId })
                .populate('service', 'name duration price category')
                .sort({ date: 1, time: 1 });
            return appointments;
        }
    }
    static async updateAppointment(appointmentId, userId, updateData) {
        const appointment = await models_1.Appointment.findOne({
            _id: appointmentId,
            user: userId
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        // If updating date/time, check availability
        if (updateData.date || updateData.time) {
            const checkDate = updateData.date || appointment.date;
            const checkTime = updateData.time || appointment.time;
            const existingAppointment = await models_1.Appointment.findOne({
                _id: { $ne: appointmentId },
                date: checkDate,
                time: checkTime,
                status: { $in: ['pending', 'confirmed'] }
            });
            if (existingAppointment) {
                throw new Error('This time slot is already booked');
            }
        }
        Object.assign(appointment, updateData);
        await appointment.save();
        await appointment.populate('service', 'name duration price category');
        return appointment;
    }
    static async cancelAppointment(appointmentId, userId) {
        const appointment = await models_1.Appointment.findOne({
            _id: appointmentId,
            user: userId
        });
        if (!appointment) {
            throw new Error('Appointment not found');
        }
        appointment.status = 'cancelled';
        await appointment.save();
    }
    static async getAppointments(query) {
        const filter = {};
        if (query.date) {
            filter.date = new Date(query.date);
        }
        if (query.status) {
            filter.status = query.status;
        }
        if (query.service) {
            filter.service = query.service;
        }
        const appointments = await models_1.Appointment.find(filter)
            .populate('user', 'name email phone')
            .populate('service', 'name duration price category')
            .sort({ date: 1, time: 1 });
        return appointments;
    }
}
exports.AppointmentService = AppointmentService;
