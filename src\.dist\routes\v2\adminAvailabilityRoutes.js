"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const adminAvailabilityController_1 = require("../../controllers/adminAvailabilityController");
const auth_1 = require("../../middleware/auth");
const router = express_1.default.Router();
// Public routes (no authentication required)
router.get('/time-slots/:date', adminAvailabilityController_1.adminAvailabilityController.getAdminTimeSlots);
router.get('/check', adminAvailabilityController_1.adminAvailabilityController.checkTimeSlotAvailability);
// Protected routes (authentication required)
router.get('/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.getGlobalSettings);
router.put('/settings', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.updateGlobalSettings);
// Get availability for a date range
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.getAvailability);
// Set availability for a specific date
router.post('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.setAvailability);
// Set availability for multiple dates (bulk operation)
router.post('/bulk', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.setBulkAvailability);
// Delete availability for a specific date
router.delete('/:date', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.deleteAvailability);
// Delete availability for multiple dates (bulk operation)
router.delete('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), adminAvailabilityController_1.adminAvailabilityController.deleteBulkAvailability);
exports.default = router;
