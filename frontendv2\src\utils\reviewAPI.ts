import { API_CONFIG } from './config';

const API_BASE_URL = API_CONFIG.BASE_URL;

export interface Review {
  _id: string;
  appointment?: string;
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  service?: {
    _id: string;
    name: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReviewRequest {
  appointmentId: string;
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: number[];
}

export interface ServiceReviewsResponse {
  reviews: Review[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: ReviewStats;
}

export const reviewAPI = {
  // Get review for a specific appointment
  async getAppointmentReview(appointmentId: string): Promise<Review | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/appointment/${appointmentId}`);

      if (response.status === 404) {
        return null; // No review found
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch appointment review');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching appointment review:', error);
      throw error;
    }
  },

  // Get all reviews for a service
  async getServiceReviews(
    serviceId: string,
    page: number = 1,
    limit: number = 10,
    status: string = 'approved'
  ): Promise<ServiceReviewsResponse> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/reviews/service/${serviceId}?page=${page}&limit=${limit}&status=${status}`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch service reviews');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error fetching service reviews:', error);
      throw error;
    }
  },

  // Create a new review
  async createReview(reviewData: CreateReviewRequest): Promise<Review> {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create review');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  },

  // Update an existing review
  async updateReview(reviewId: string, updateData: Partial<CreateReviewRequest>): Promise<Review> {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update review');
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  },

  // Delete a review
  async deleteReview(reviewId: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete review');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }
};
