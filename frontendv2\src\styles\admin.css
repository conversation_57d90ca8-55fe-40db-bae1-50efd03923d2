/* Admin Dashboard Styles */

/* Review Management Styles */
.review-management {
  padding: 20px;
  max-width: 100%;
}

.review-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.review-management-header .header-content h2 {
  margin: 0 0 5px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.review-management-header .header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.review-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.review-selection {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.review-selection input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.review-info {
  flex: 1;
  min-width: 0;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.customer-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.verified-badge {
  background: #10b981;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.review-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.rating-stars .star {
  color: #fbbf24;
  font-size: 16px;
}

.rating-stars .star.empty {
  color: #d1d5db;
}

.interactive-stars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.interactive-star {
  background: none;
  border: none;
  font-size: 24px;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.2s;
  padding: 4px;
}

.interactive-star.filled {
  color: #fbbf24;
}

.interactive-star:hover {
  color: #f59e0b;
}

.rating-label {
  margin-left: 8px;
  font-size: 14px;
  color: #6b7280;
}

.status-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.approved {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.rejected {
  background: #fee2e2;
  color: #991b1b;
}

.review-actions {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.review-actions .btn {
  min-width: auto;
  padding: 6px 8px;
  font-size: 14px;
}

.review-content {
  margin-top: 16px;
}

.service-info {
  margin-bottom: 12px;
  font-size: 14px;
  color: #6b7280;
}

.appointment-info {
  margin-left: 8px;
  font-size: 13px;
}

.review-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.review-comment {
  margin: 0 0 12px 0;
  color: #374151;
  line-height: 1.5;
}

.admin-response {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.admin-response-label {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 14px;
}

.admin-response-text {
  color: #374151;
  line-height: 1.5;
  margin-bottom: 4px;
}

.admin-response-date {
  font-size: 12px;
  color: #6b7280;
}

.review-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
  font-size: 13px;
  color: #6b7280;
}

.helpful-votes {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Review Modal Styles */
.review-modal-form {
  max-width: 600px;
}

.review-info-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.review-info-section .customer-info {
  margin-bottom: 12px;
}

.review-info-section .service-info {
  margin-bottom: 12px;
  font-size: 14px;
}

.review-info-section .appointment-info {
  margin-top: 4px;
  font-size: 13px;
  color: #6b7280;
}

.review-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dates-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-display {
  padding: 12px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #374151;
  min-height: 20px;
}

.form-display:empty::before {
  content: "No content";
  color: #9ca3af;
  font-style: italic;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state-subtitle {
  font-size: 16px;
  margin: 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding: 20px;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

/* Guest Login Styles */
.guest-login-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
}

.guest-login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.guest-login-header h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.guest-login-header p {
  margin: 0 0 32px 0;
  color: #6b7280;
  font-size: 16px;
}

.guest-login-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.guest-login-btn {
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  display: inline-block;
}

.guest-login-btn.primary {
  background: #3b82f6;
  color: white;
}

.guest-login-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.guest-login-btn.secondary {
  background: #f8fafc;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.guest-login-btn.secondary:hover {
  background: #f1f5f9;
  border-color: #d1d5db;
}

.guest-login-form {
  text-align: left;
}

.guest-login-form .form-group {
  margin-bottom: 24px;
}

.guest-login-form .form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.guest-login-form .form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.guest-login-form .form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.guest-login-form .form-help {
  display: block;
  margin-top: 4px;
  font-size: 13px;
  color: #6b7280;
}

.guest-login-form .form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.guest-login-form .btn-submit {
  background: #3b82f6;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.guest-login-form .btn-submit:hover:not(:disabled) {
  background: #2563eb;
}

.guest-login-form .btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.guest-login-form .btn-cancel {
  background: #f8fafc;
  color: #374151;
  padding: 12px 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.guest-login-form .btn-cancel:hover {
  background: #f1f5f9;
  border-color: #d1d5db;
}

.guest-login-footer {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f3f4f6;
}

.guest-login-footer p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Dashboard Styles for Reviews Page */
.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.dashboard-subtitle {
  font-size: 18px;
  color: #6b7280;
  margin: 0;
}

.dashboard-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* Appointments Grid for Reviews */
.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.appointment-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
}

.appointment-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.appointment-service {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.appointment-date {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.appointment-details {
  margin-bottom: 20px;
}

.service-category {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 4px 0;
}

.appointment-time {
  color: #374151;
  font-size: 14px;
  margin: 0;
}

.appointment-actions {
  display: flex;
  justify-content: flex-end;
}

.write-review-btn {
  background: #3b82f6;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.write-review-btn:hover {
  background: #2563eb;
}

/* Review Form Container */
.review-form-container {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
  margin-top: 20px;
}

.review-form-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

/* Reviews Grid */
.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.review-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.review-service-info {
  flex: 1;
}

.review-service-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.review-date {
  font-size: 14px;
  color: #6b7280;
}

.review-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.review-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.review-comment {
  color: #374151;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .appointments-grid,
  .reviews-grid {
    grid-template-columns: 1fr;
  }

  .guest-login-card {
    padding: 24px;
    margin: 0 16px;
  }

  .guest-login-options {
    gap: 12px;
  }

  .guest-login-form .form-actions {
    flex-direction: column;
  }

  .dashboard-title {
    font-size: 28px;
  }

  .dashboard-subtitle {
    font-size: 16px;
  }
}

.appointment-management {
  padding: 20px;
  max-width: 100%;
}

.appointment-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.page-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
}

.loading-container p,
.error-container p {
  color: #6b7280;
  font-size: 16px;
}

.header-actions .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-outline {
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

/* Error Message */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-close {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

/* Enhanced Service Filters */
.appointment-filters {
  background: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* Bulk actions styling */
.bulk-actions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.bulk-actions-buttons {
  display: flex;
  gap: 8px;
}

.bulk-actions-buttons .btn {
  font-size: 13px;
  padding: 8px 16px;
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 20px;
  align-items: end;
  margin-bottom: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 5px;
  font-size: 14px;
}

.search-input-container {
  position: relative;
}

.search-input, .filter-select, .filter-input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus, .filter-select:focus, .filter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
  padding-right: 40px;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  width: 16px;
  height: 16px;
}

.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #eff6ff;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 15px 0;
}

.selected-count {
  font-weight: 600;
  color: #1d4ed8;
}

.bulk-actions-buttons {
  position: relative;
}

.bulk-actions-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 180px;
}

.bulk-action-btn {
  display: block;
  width: 100%;
  padding: 10px 16px;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.bulk-action-btn:hover {
  background-color: #f3f4f6;
}

.quick-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 20px 0;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
}

.quick-filters-label {
  font-weight: 600;
  color: #374151;
  margin-right: 10px;
}

.quick-filter-btn {
  padding: 8px 16px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  color: #374151;
  font-weight: 500;
  transition: all 0.2s;
}

.quick-filter-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  font-size: 14px;
  color: #6b7280;
}

.active-filters {
  display: flex;
  gap: 8px;
}

.filter-tag {
  background-color: #dbeafe;
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Enhanced Service List */
.appointment-list-loading, .appointment-list-empty {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.appointment-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.appointment-table {
  width: 100%;
  border-collapse: collapse;
}

.appointment-table th {
  background-color: #f8fafc;
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.appointment-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.appointment-table th.sortable:hover {
  background-color: #f3f4f6;
}

.appointment-table td {
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.appointment-row:hover {
  background-color: #f9fafb;
}

.checkbox-column {
  width: 50px;
}

.customer-info {
  min-width: 200px;
}

.customer-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.customer-email {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 2px;
}

.customer-phone {
  color: #6b7280;
  font-size: 12px;
}

.service-info {
  min-width: 150px;
}

.service-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.service-category {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 2px;
}

.service-duration {
  color: #6b7280;
  font-size: 12px;
}

.datetime-info {
  min-width: 120px;
}

.date {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.time {
  color: #6b7280;
  font-size: 14px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: none;
  cursor: pointer;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-confirmed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-completed {
  background-color: #e0e7ff;
  color: #3730a3;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

.price {
  font-weight: 600;
  color: #059669;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.action-buttons .btn {
  padding: 8px 10px;
  font-size: 12px;
  border-radius: 6px;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 12px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-sm { max-width: 400px; }
.modal-md { max-width: 600px; }
.modal-lg { max-width: 800px; }
.modal-xl { max-width: 1200px; }

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.modal-close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 24px;
}

/* Form Styles */
.appointment-form {
  max-width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
  font-size: 14px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-section {
  margin: 30px 0;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.form-section h4 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.form-display-value {
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
}

.error-text {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 20px 0;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:hover:not(.disabled) {
  background-color: #f3f4f6;
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  min-width: 40px;
  text-align: center;
}

.pagination-page:hover:not(.dots) {
  background-color: #f3f4f6;
}

.pagination-page.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.pagination-page.dots {
  cursor: default;
  border: none;
  background: none;
}

/* Analytics Styles */
.analytics-dashboard {
  padding: 20px;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.date-range-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.quick-ranges {
  display: flex;
  gap: 8px;
}

.quick-range-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.quick-range-btn:hover {
  background-color: #f3f4f6;
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.metric-icon {
  font-size: 20px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.metric-change {
  font-size: 14px;
  font-weight: 600;
}

.metric-change.positive { color: #059669; }
.metric-change.negative { color: #dc2626; }
.metric-change.neutral { color: #6b7280; }

.analytics-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.analytics-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.status-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f9fafb;
}

.status-card.status-pending { background-color: #fef3c7; }
.status-card.status-confirmed { background-color: #d1fae5; }
.status-card.status-completed { background-color: #e0e7ff; }
.status-card.status-cancelled { background-color: #fee2e2; }

.status-count {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.status-percentage {
  font-size: 12px;
  color: #6b7280;
}

.chart-container {
  margin: 20px 0;
}

.analytics-chart {
  position: relative;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.service-stats {
  max-height: 400px;
  overflow-y: auto;
}

.service-stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.service-rank {
  font-weight: 700;
  color: #6b7280;
  min-width: 30px;
}

.service-info {
  flex: 1;
}

.service-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.service-metrics {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.service-bar {
  width: 100px;
  height: 8px;
  background-color: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.service-bar-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.revenue-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.insight-card {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  text-align: center;
}

.insight-card h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.insight-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.insight-card p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

/* Services Page Styles */
.service-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.service-tab {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.service-tab:hover {
  color: #374151;
}

.service-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.services-section {
  margin-bottom: 40px;
}

.services-section h2 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.service-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
  position: relative;
}

.service-card:hover {
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: #cbd5e1;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.service-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 700;
  flex: 1;
}

.service-price {
  color: #059669;
  font-size: 18px;
  font-weight: 700;
}

.service-description {
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.5;
}

.service-details {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.service-details span {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.service-category {
  background-color: #f3f4f6;
  color: #374151;
}

.service-duration {
  background-color: #dbeafe;
  color: #1e40af;
}

.service-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.service-status.active {
  background-color: #d1fae5;
  color: #065f46;
}

.service-status.inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.addons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.addon-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.addon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.addon-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.addon-price {
  color: #059669;
  font-size: 16px;
  font-weight: 700;
}

.addon-details {
  margin-bottom: 16px;
}

.addon-details p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
}

.addon-description {
  color: #374151 !important;
  font-style: italic;
}

.addon-actions {
  display: flex;
  gap: 8px;
}

.services-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 40px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-number {
  margin: 0;
  color: #1f2937;
  font-size: 32px;
  font-weight: 700;
}

/* Settings Page Styles */
.settings-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-tab {
  padding: 12px 24px;
  border: none;
  background: none;
  color: #6b7280;
  font-weight: 600;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.settings-tab:hover {
  color: #374151;
}

.settings-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.settings-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.settings-section h2 {
  margin: 0 0 24px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:disabled,
.form-group textarea:disabled,
.form-group select:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.payment-method-group {
  padding: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

.payment-method-group h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.payment-method-group .form-group {
  margin-bottom: 16px;
}

.payment-method-group .form-group:last-child {
  margin-bottom: 0;
}

/* Service Management Specific Styles */
.service-management {
  padding: 20px;
  max-width: 100%;
  background-color: #f8fafc;
  min-height: 100vh;
}

.service-card {
  position: relative;
}

/* Enhanced Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.page-header h1 {
  margin: 0;
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
}

.page-header p {
  margin: 4px 0 0 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Flex utilities */
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Text utilities */
.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-900 {
  color: #111827;
}

.text-blue-800 {
  color: #1e40af;
}

.text-blue-900 {
  color: #1e3a8a;
}

.text-sm {
  font-size: 0.875rem;
}

.text-center {
  text-align: center;
}

/* Background utilities */
.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-yellow-200 {
  background-color: #fef08a;
}

/* Border utilities */
.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.rounded-lg {
  border-radius: 0.5rem;
}

/* Padding utilities */
.p-4 {
  padding: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

/* Height utilities */
.max-h-60 {
  max-height: 15rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* Font utilities */
.font-medium {
  font-weight: 500;
}

/* Mark highlighting */
mark {
  background-color: #fef08a;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* View mode toggle styles */
.view-mode-toggle {
  display: flex;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.view-mode-toggle button {
  padding: 8px 12px;
  border: none;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-mode-toggle button.active {
  background: #3b82f6;
  color: white;
}

.view-mode-toggle button:hover:not(.active) {
  background: #f3f4f6;
}

/* Enhanced empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin: 24px 0;
}

.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  color: #374151;
  margin-bottom: 8px;
  font-size: 18px;
}

.empty-state p {
  color: #6b7280;
  margin-bottom: 24px;
}

/* Service info styling */
.service-info {
  min-width: 250px;
}

.service-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  font-size: 16px;
}

/* Enhanced status badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: none;
  cursor: default;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Additional service management styles */
.service-management .page-header h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.service-management .appointment-filters {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
}

.service-management .appointment-table-container {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.service-management .services-grid {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Improved action buttons */
.action-buttons .btn {
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Loading animation enhancement */
.loading-spinner {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  position: relative;
}

.loading-spinner::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: white;
  border-radius: 50%;
}

/* Search input enhancement */
.search-input-container {
  position: relative;
}

.search-input-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 8px;
  padding: 1px;
  z-index: -1;
}

.search-input {
  position: relative;
  z-index: 1;
}

/* Enhanced hover effects */
.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.service-card:hover::before {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }

  .header-actions {
    width: 100%;
    justify-content: stretch;
  }

  .header-actions button {
    flex: 1;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .filter-group {
    min-width: 100%;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .appointment-table-container {
    overflow-x: auto;
  }

  .appointment-table {
    min-width: 800px;
  }

  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range-controls {
    flex-direction: column;
    gap: 15px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .addons-grid {
    grid-template-columns: 1fr;
  }

  .services-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .service-actions,
  .addon-actions {
    flex-direction: column;
  }

  .service-details {
    flex-direction: column;
    gap: 8px;
  }

  .quick-filters {
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-filter-btn {
    font-size: 12px;
    padding: 6px 12px;
  }

  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .bulk-actions-buttons {
    display: flex;
    gap: 8px;
  }

  .bulk-actions-buttons button {
    flex: 1;
  }
}
