import { useState } from 'react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';
import './AdminCalendar.css';

interface Props {
  onDateSelect?: (date: Date) => void;
}

export default function AppointmentCalendar({ onDateSelect }: Props) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  // Handle date selection
  // @ts-ignore - ignoring type issues with react-calendar
  const handleDateSelect = (value) => {
    if (value instanceof Date) {
      setSelectedDate(value);
      if (onDateSelect) {
        onDateSelect(value);
      }
    }
  };

  return (
    <div className="calendar-section">
      <Calendar
        onChange={handleDateSelect}
        value={selectedDate}
        minDate={new Date()}
        className="react-calendar"
        tileClassName={({ date }) => {
          if (date.toDateString() === selectedDate.toDateString()) {
            return 'selected-date';
          }
          return '';
        }}
      />
    </div>
  );
}
