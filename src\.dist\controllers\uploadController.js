"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const response_1 = require("../utils/response");
const config_1 = require("../config");
const cloudinaryService_1 = require("../services/cloudinaryService");
// Legacy local storage configuration (kept for backward compatibility)
const localStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadPath = config_1.config.UPLOAD.UPLOAD_PATH;
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(uploadPath)) {
            fs_1.default.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        // Generate unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path_1.default.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + extension);
    }
});
// Legacy file filter
const legacyFileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
    }
};
// Legacy multer configuration
const legacyUpload = (0, multer_1.default)({
    storage: localStorage,
    fileFilter: legacyFileFilter,
    limits: {
        fileSize: config_1.config.UPLOAD.MAX_FILE_SIZE // 5MB default
    }
});
class UploadController {
    // Check Cloudinary configuration
    static async checkCloudinaryConfig(req, res) {
        try {
            const isConfigured = (0, cloudinaryService_1.validateCloudinaryConfig)();
            if (isConfigured) {
                (0, response_1.sendSuccess)(res, 'Cloudinary is properly configured');
            }
            else {
                (0, response_1.sendError)(res, 'Cloudinary configuration is missing or incomplete');
            }
        }
        catch (error) {
            console.error('Cloudinary config check error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Upload single file to Cloudinary with type parameter
    static async uploadSingleFileToCloudinary(req, res) {
        try {
            const uploadType = req.params.uploadType;
            // Validate upload type
            const validUploadTypes = [
                'profilePicture', 'productImage', 'serviceImage', 'brandingImage',
                'testimonialImage', 'staffImage', 'businessDocs', 'logo',
                'favicon', 'heroImage', 'galleryImage'
            ];
            if (!validUploadTypes.includes(uploadType)) {
                (0, response_1.sendError)(res, `Invalid upload type. Valid types: ${validUploadTypes.join(', ')}`);
                return;
            }
            if (!req.file) {
                (0, response_1.sendError)(res, 'No file provided');
                return;
            }
            const fileUrl = await (0, cloudinaryService_1.uploadSingleFile)(req, uploadType);
            if (!fileUrl) {
                (0, response_1.sendError)(res, 'Failed to upload file to Cloudinary');
                return;
            }
            (0, response_1.sendSuccess)(res, `${uploadType} uploaded successfully to Cloudinary`, {
                originalName: req.file.originalname,
                size: req.file.size,
                url: fileUrl,
                uploadType
            });
        }
        catch (error) {
            console.error('Upload single file to Cloudinary error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Legacy local upload (kept for backward compatibility)
    static async uploadImage(req, res) {
        try {
            if (!req.file) {
                (0, response_1.sendError)(res, 'No image file provided');
                return;
            }
            const imageUrl = `/uploads/${req.file.filename}`;
            (0, response_1.sendSuccess)(res, 'Image uploaded successfully', {
                filename: req.file.filename,
                originalName: req.file.originalname,
                size: req.file.size,
                url: imageUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${imageUrl}`
            });
        }
        catch (error) {
            console.error('Upload image error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Upload multiple files to Cloudinary with type parameter
    static async uploadMultipleFilesToCloudinary(req, res) {
        try {
            const uploadType = req.params.uploadType;
            // Validate upload type
            const validUploadTypes = [
                'profilePicture', 'productImage', 'serviceImage', 'brandingImage',
                'testimonialImage', 'staffImage', 'businessDocs', 'logo',
                'favicon', 'heroImage', 'galleryImage'
            ];
            if (!validUploadTypes.includes(uploadType)) {
                (0, response_1.sendError)(res, `Invalid upload type. Valid types: ${validUploadTypes.join(', ')}`);
                return;
            }
            const files = req.files;
            if (!files || files.length === 0) {
                (0, response_1.sendError)(res, 'No files provided');
                return;
            }
            const fileUrls = await (0, cloudinaryService_1.uploadMultipleFiles)(req, uploadType);
            if (fileUrls.length === 0) {
                (0, response_1.sendError)(res, 'Failed to upload files to Cloudinary');
                return;
            }
            const uploadedFiles = files.map((file, index) => ({
                originalName: file.originalname,
                size: file.size,
                url: fileUrls[index] || null
            })).filter(file => file.url !== null);
            (0, response_1.sendSuccess)(res, `${uploadType} files uploaded successfully to Cloudinary`, {
                files: uploadedFiles,
                count: uploadedFiles.length,
                uploadType
            });
        }
        catch (error) {
            console.error('Upload multiple files to Cloudinary error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Legacy multiple upload (kept for backward compatibility)
    static async uploadMultipleImages(req, res) {
        try {
            const uploadMultiple = legacyUpload.array('images', 10); // Max 10 images
            uploadMultiple(req, res, (err) => {
                if (err) {
                    console.error('Upload multiple images error:', err);
                    (0, response_1.sendError)(res, err.message);
                    return;
                }
                const files = req.files;
                if (!files || files.length === 0) {
                    (0, response_1.sendError)(res, 'No image files provided');
                    return;
                }
                const uploadedImages = files.map(file => ({
                    filename: file.filename,
                    originalName: file.originalname,
                    size: file.size,
                    url: `/uploads/${file.filename}`,
                    fullUrl: `${req.protocol}://${req.get('host')}/uploads/${file.filename}`
                }));
                (0, response_1.sendSuccess)(res, 'Images uploaded successfully', {
                    images: uploadedImages,
                    count: uploadedImages.length
                });
            });
        }
        catch (error) {
            console.error('Upload multiple images error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Delete image from Cloudinary - supports both URLs and public IDs
    static async deleteImageFromCloudinary(req, res) {
        try {
            const { imageUrl, publicId } = req.body;
            const imageUrlOrPublicId = imageUrl || publicId;
            if (!imageUrlOrPublicId) {
                (0, response_1.sendError)(res, 'Image URL or public ID is required');
                return;
            }
            const deleted = await (0, cloudinaryService_1.deleteFromCloudinary)(imageUrlOrPublicId);
            if (deleted) {
                (0, response_1.sendSuccess)(res, 'Image deleted successfully from Cloudinary');
            }
            else {
                (0, response_1.sendError)(res, 'Failed to delete image from Cloudinary');
            }
        }
        catch (error) {
            console.error('Delete image from Cloudinary error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Get optimized image URL
    static async getOptimizedImage(req, res) {
        try {
            const { imageUrl } = req.params;
            const { width, height, quality, format, crop } = req.query;
            if (!imageUrl) {
                (0, response_1.sendError)(res, 'Image URL is required');
                return;
            }
            const optimizedUrl = (0, cloudinaryService_1.getOptimizedImageUrl)(decodeURIComponent(imageUrl), {
                width: width ? parseInt(width) : undefined,
                height: height ? parseInt(height) : undefined,
                quality: quality,
                format: format,
                crop: crop,
            });
            (0, response_1.sendSuccess)(res, 'Optimized image URL generated', {
                originalUrl: imageUrl,
                optimizedUrl
            });
        }
        catch (error) {
            console.error('Get optimized image error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Legacy delete (kept for backward compatibility)
    static async deleteImage(req, res) {
        try {
            const { filename } = req.params;
            if (!filename) {
                (0, response_1.sendError)(res, 'Filename is required');
                return;
            }
            const filePath = path_1.default.join(config_1.config.UPLOAD.UPLOAD_PATH, filename);
            // Check if file exists
            if (!fs_1.default.existsSync(filePath)) {
                (0, response_1.sendError)(res, 'File not found', undefined, 404);
                return;
            }
            // Delete file
            fs_1.default.unlinkSync(filePath);
            (0, response_1.sendSuccess)(res, 'Image deleted successfully');
        }
        catch (error) {
            console.error('Delete image error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getImageInfo(req, res) {
        try {
            const { filename } = req.params;
            if (!filename) {
                (0, response_1.sendError)(res, 'Filename is required');
                return;
            }
            const filePath = path_1.default.join(config_1.config.UPLOAD.UPLOAD_PATH, filename);
            // Check if file exists
            if (!fs_1.default.existsSync(filePath)) {
                (0, response_1.sendError)(res, 'File not found', undefined, 404);
                return;
            }
            const stats = fs_1.default.statSync(filePath);
            const imageUrl = `/uploads/${filename}`;
            (0, response_1.sendSuccess)(res, 'Image info retrieved successfully', {
                filename,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                url: imageUrl,
                fullUrl: `${req.protocol}://${req.get('host')}${imageUrl}`
            });
        }
        catch (error) {
            console.error('Get image info error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Public payment proof upload (no authentication required)
    static async uploadPaymentProof(req, res) {
        try {
            if (!req.file) {
                (0, response_1.sendError)(res, 'No file uploaded', undefined, 400);
                return;
            }
            const { email, appointmentData } = req.body;
            if (!email) {
                (0, response_1.sendError)(res, 'Email is required', undefined, 400);
                return;
            }
            console.log(`Starting payment proof upload for email: ${email}, file: ${req.file.originalname}`);
            // Upload to Cloudinary with better error handling
            try {
                const result = await (0, cloudinaryService_1.uploadSingleFile)(req, 'paymentProof');
                if (!result) {
                    (0, response_1.sendError)(res, 'Failed to upload file to Cloudinary', undefined, 500);
                    return;
                }
                // Parse appointment data
                let appointmentInfo = {};
                if (appointmentData) {
                    try {
                        appointmentInfo = JSON.parse(appointmentData);
                    }
                    catch (parseError) {
                        console.error('Error parsing appointment data:', parseError);
                        // Continue with empty object if parsing fails
                    }
                }
                console.log(`Payment proof uploaded successfully: ${result}`);
                // If we have appointment data, create the appointment with payment proof
                if (appointmentInfo.selectedService && appointmentInfo.selectedDate && appointmentInfo.selectedTime && appointmentInfo.customerInfo) {
                    try {
                        // Import required models
                        const { Appointment, Service, User } = require('../models');
                        // Convert date to UTC to avoid timezone issues
                        const appointmentDate = new Date(appointmentInfo.selectedDate + 'T00:00:00.000Z');
                        // Find or create user based on email
                        let user = null;
                        if (appointmentInfo.userId) {
                            user = await User.findById(appointmentInfo.userId);
                        }
                        else if (appointmentInfo.customerInfo.email) {
                            user = await User.findOne({ email: appointmentInfo.customerInfo.email.toLowerCase() });
                        }
                        // Get service details
                        const service = await Service.findById(appointmentInfo.selectedService.id);
                        if (!service) {
                            (0, response_1.sendError)(res, 'Service not found', undefined, 404);
                            return;
                        }
                        // Calculate total price
                        const totalPrice = appointmentInfo.selectedService.price + (appointmentInfo.selectedAddOns || []).reduce((sum, addon) => sum + (addon.price || 0), 0);
                        // Create appointment with payment proof
                        const appointment = await Appointment.create({
                            user: user ? user._id : undefined,
                            service: appointmentInfo.selectedService.id,
                            date: appointmentDate,
                            time: appointmentInfo.selectedTime,
                            status: 'pending',
                            paymentStatus: 'pending',
                            customerInfo: {
                                name: `${appointmentInfo.customerInfo.firstName} ${appointmentInfo.customerInfo.lastName}`.trim(),
                                email: appointmentInfo.customerInfo.email,
                                phone: appointmentInfo.customerInfo.phone
                            },
                            totalPrice: totalPrice,
                            paymentProofs: [{
                                    id: new Date().getTime().toString(), // Temporary ID
                                    amount: appointmentInfo.paymentAmount || totalPrice,
                                    paymentMethod: appointmentInfo.paymentMethod || 'unknown',
                                    proofImage: result, // Cloudinary URL
                                    status: 'pending',
                                    notes: `Payment proof uploaded via ${appointmentInfo.paymentMethod || 'unknown'}`,
                                    createdAt: new Date()
                                }]
                        });
                        // Populate service data for response
                        await appointment.populate('service', 'name price duration category');
                        (0, response_1.sendSuccess)(res, 'Payment proof uploaded and appointment created successfully', {
                            url: result,
                            email: email,
                            appointmentData: appointmentInfo,
                            appointment: {
                                id: appointment._id,
                                serviceId: appointment.service._id,
                                serviceName: appointment.service.name,
                                date: appointment.date,
                                time: appointment.time,
                                status: appointment.status,
                                customerInfo: appointment.customerInfo,
                                totalPrice: appointment.totalPrice,
                                paymentProofs: appointment.paymentProofs
                            },
                            uploadedAt: new Date().toISOString(),
                            fileName: req.file.originalname
                        });
                    }
                    catch (appointmentError) {
                        console.error('Error creating appointment:', appointmentError);
                        // Still return success for the upload, but note the appointment creation failed
                        (0, response_1.sendSuccess)(res, 'Payment proof uploaded successfully, but appointment creation failed', {
                            url: result,
                            email: email,
                            appointmentData: appointmentInfo,
                            uploadedAt: new Date().toISOString(),
                            fileName: req.file.originalname,
                            appointmentError: appointmentError.message
                        });
                    }
                }
                else {
                    // No appointment data, just return upload success
                    (0, response_1.sendSuccess)(res, 'Payment proof uploaded successfully', {
                        url: result,
                        email: email,
                        appointmentData: appointmentInfo,
                        uploadedAt: new Date().toISOString(),
                        fileName: req.file.originalname
                    });
                }
            }
            catch (uploadError) {
                console.error('Cloudinary upload error:', uploadError);
                // Provide specific error messages based on error type
                if (uploadError.message?.includes('timeout') || uploadError.message?.includes('Timeout')) {
                    (0, response_1.sendError)(res, 'Upload timeout - please try again with a smaller file or check your internet connection', undefined, 408);
                }
                else if (uploadError.message?.includes('File too large')) {
                    (0, response_1.sendError)(res, 'File is too large - please use a file smaller than 10MB', undefined, 413);
                }
                else {
                    (0, response_1.sendError)(res, `Upload failed: ${uploadError.message}`, undefined, 500);
                }
            }
        }
        catch (error) {
            console.error('Payment proof upload error:', error);
            (0, response_1.sendError)(res, `Server error: ${error.message}`, undefined, 500);
        }
    }
}
exports.UploadController = UploadController;
// Legacy middleware for backward compatibility
UploadController.uploadMiddleware = legacyUpload.single('image');
// Cloudinary middleware
UploadController.cloudinaryUploadSingle = (fieldName = 'image') => (0, cloudinaryService_1.uploadSingle)(fieldName);
UploadController.cloudinaryUploadMultiple = (fieldName = 'images', maxCount = 10) => (0, cloudinaryService_1.uploadMultiple)(fieldName, maxCount);
