import { NavLink } from 'react-router-dom';

export default function AdminNav() {
  return (
    <nav className="admin-nav">
      <NavLink to="/admin" end className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        📊 DASHBOARD
      </NavLink>
      <NavLink to="/admin/appointments" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        📅 APPOINTMENTS
      </NavLink>
      <NavLink to="/admin/calendar" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        🗓️ AVAILABILITY
      </NavLink>
      <NavLink to="/admin/customers" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        👥 CUSTOMERS
      </NavLink>
      <NavLink to="/admin/services" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        💇‍♀️ SERVICES
      </NavLink>
      <NavLink to="/admin/reviews" className={({ isActive }) => isActive ? 'nav-link active' : 'nav-link'}>
        ⭐ REVIEWS
      </NavLink>
    </nav>
  );
}
