.admin-calendar {
  padding: 24px;
}

.calendar-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-top: 24px;
}

.calendar-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.availability-section {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.availability-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.full-day-control {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f8f8;
  border-radius: 4px;
}

.full-day-control label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.reason-input {
  margin-bottom: 20px;
}

.reason-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.reason-input input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.time-slot {
  padding: 8px;
  background: #f8f8f8;
  border-radius: 4px;
}

.time-slot label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.save-button {
  width: 100%;
  padding: 12px;
  background: #000;
  color: #fff;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-button:hover {
  background: #333;
}

.save-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  margin-top: 16px;
  padding: 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
}

/* React Calendar Overrides */
.react-calendar {
  width: 100%;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.react-calendar__tile--active {
  background: #000 !important;
}

.react-calendar__tile--now {
  background: #f0f0f0;
}

.react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: #333 !important;
  color: white;
}

/* Enhanced Calendar Styles */
.global-controls {
  display: flex;
  gap: 12px;
  margin: 16px 0;
  flex-wrap: wrap;
}

.global-settings-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.status-available {
  color: #28a745;
  font-weight: bold;
}

.status-unavailable {
  color: #dc3545;
  font-weight: bold;
}

.bulk-controls {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.selected-bulk {
  background: #2196f3 !important;
  color: white !important;
}

.time-slot-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-small:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #ccc;
}

/* Removed duplicate .no-slots - see updated version below */

/* Availability Status Styles */
.availability-status {
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.custom {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.status-badge.default {
  background: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #ce93d8;
}

.status-badge.unavailable {
  background: #ffebee;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
}

.reason {
  margin: 0;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.reason-text {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.status-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.no-slots {
  text-align: center;
  color: #666;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.no-slots p {
  margin: 8px 0;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: 1px solid #007bff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Bulk Operations Styles */
.bulk-operations {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.bulk-operations h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 16px;
}

.bulk-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.bulk-buttons .btn-primary {
  background-color: #28a745;
  border-color: #28a745;
}

.bulk-buttons .btn-primary:hover:not(:disabled) {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Time Slot Controls Enhancement */
.time-slot-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.time-slot-controls .btn-primary {
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.time-slot-controls .btn-primary:hover:not(:disabled) {
  background-color: #138496;
  border-color: #117a8b;
}

.success-message {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  color: #0369a1;
}

.help-text {
  font-size: 14px;
  color: #6b7280;
  margin-top: 8px;
  font-style: italic;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #000;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #333;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .calendar-container {
    grid-template-columns: 1fr;
  }

  .time-slots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }

  .global-controls {
    flex-direction: column;
  }

  .bulk-buttons {
    flex-direction: column;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }
}
