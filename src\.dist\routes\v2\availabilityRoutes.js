"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const adminAvailabilityController_1 = require("../../controllers/adminAvailabilityController");
const router = express_1.default.Router();
// Public routes for checking availability (no authentication required)
// Get time slots with availability status for a specific date (public)
router.get('/time-slots/:date', adminAvailabilityController_1.adminAvailabilityController.getAdminTimeSlots);
// Check if a specific time slot is available
router.get('/check', adminAvailabilityController_1.adminAvailabilityController.checkTimeSlotAvailability);
// Admin endpoints made public for guest access
router.get('/admin/availability/time-slots/:date', adminAvailabilityController_1.adminAvailabilityController.getAdminTimeSlots);
router.get('/admin/availability/check', adminAvailabilityController_1.adminAvailabilityController.checkTimeSlotAvailability);
exports.default = router;
