import AdminAvailability, { IAdminAvailability } from '../models/adminAvailability';
import { format, parseISO } from 'date-fns';

export const availabilityService = {
  // Get availability for a date range
  async getAvailability(startDate: string, endDate: string) {
    return await AdminAvailability.find({
      date: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    }).sort({ date: 1 });
  },

  // Get availability for a specific date
  async getDateAvailability(date: string) {
    const availability = await AdminAvailability.findOne({
      date: new Date(date)
    });

    if (!availability) {
      // Return default time slots if no availability is set
      return {
        date,
        timeSlots: generateDefaultTimeSlots(),
        isFullDayUnavailable: false
      };
    }

    return availability;
  },

  // Set availability for a date
  async setAvailability(data: Partial<IAdminAvailability>) {
    const { date, timeSlots, isFullDayUnavailable, reason } = data;
    
    if (!date) {
      throw new Error('Date is required');
    }

    return await AdminAvailability.findOneAndUpdate(
      { date: new Date(date) },
      {
        date: new Date(date),
        timeSlots,
        isFullDayUnavailable,
        reason
      },
      { upsert: true, new: true }
    );
  },

  // Delete availability for a date
  async deleteAvailability(date: string) {
    return await AdminAvailability.deleteOne({
      date: new Date(date)
    });
  },

  // Check if a specific date and time is available
  async isTimeSlotAvailable(date: string, time: string): Promise<boolean> {
    const availability = await this.getDateAvailability(date);

    if (availability.isFullDayUnavailable) {
      return false;
    }

    const timeSlot = availability.timeSlots?.find(slot => slot.time === time);
    if (!timeSlot) {
      // If no specific time slot is set, assume it's available
      return true;
    }

    return timeSlot.isAvailable;
  }
};

// Helper function to generate default time slots (9 AM to 5 PM)
function generateDefaultTimeSlots() {
  const timeSlots = [];
  for (let hour = 9; hour < 17; hour++) {
    timeSlots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      isAvailable: true
    });
  }
  return timeSlots;
}
