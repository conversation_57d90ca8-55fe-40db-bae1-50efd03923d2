import { useState, useEffect } from 'react';
import { reviewAPI, type Review, type CreateReviewRequest } from '../utils/reviewAPI';
import { useToast } from '../contexts/ToastContext';

interface AppointmentReviewProps {
  appointmentId: string;
  appointmentStatus: string;
  customerName: string;
  serviceName: string;
  onReviewSubmitted?: (review: Review) => void;
}

export default function AppointmentReview({
  appointmentId,
  appointmentStatus,
  customerName,
  serviceName,
  onReviewSubmitted
}: AppointmentReviewProps) {
  const [review, setReview] = useState<Review | null>(null);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    comment: '',
    customerName: customerName
  });
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    loadReview();
  }, [appointmentId]);

  const loadReview = async () => {
    try {
      setLoading(true);
      const existingReview = await reviewAPI.getAppointmentReview(appointmentId);
      setReview(existingReview);
    } catch (error) {
      console.error('Error loading review:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.rating < 1 || formData.rating > 5) {
      showError('Please select a rating between 1 and 5 stars');
      return;
    }

    setSubmitting(true);
    try {
      const reviewData: CreateReviewRequest = {
        appointmentId,
        rating: formData.rating,
        title: formData.title.trim() || undefined,
        comment: formData.comment.trim() || undefined,
        customerName: formData.customerName.trim() || customerName
      };

      const newReview = await reviewAPI.createReview(reviewData);
      setReview(newReview);
      setShowReviewForm(false);
      
      if (onReviewSubmitted) {
        onReviewSubmitted(newReview);
      }

      showSuccess('Thank you for your review!');
    } catch (error: any) {
      console.error('Error submitting review:', error);
      showError(error.message || 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number, interactive: boolean = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type={interactive ? 'button' : undefined}
            className={`text-2xl ${
              star <= rating 
                ? 'text-yellow-400' 
                : 'text-gray-300'
            } ${interactive ? 'hover:text-yellow-400 cursor-pointer' : ''}`}
            onClick={interactive && onRatingChange ? () => onRatingChange(star) : undefined}
            disabled={!interactive}
          >
            ★
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">
          {rating} out of 5 stars
        </span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  // Show existing review
  if (review) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Your Review</h3>
          <span className="text-sm text-gray-500">
            {new Date(review.createdAt).toLocaleDateString()}
          </span>
        </div>
        
        <div className="space-y-4">
          <div>
            <div className="flex items-center space-x-2 mb-2">
              {renderStars(review.rating)}
            </div>
            <p className="text-sm text-gray-600">Service: {serviceName}</p>
          </div>
          
          {review.title && (
            <div>
              <h4 className="font-medium text-gray-900">{review.title}</h4>
            </div>
          )}
          
          {review.comment && (
            <div>
              <p className="text-gray-700">{review.comment}</p>
            </div>
          )}
          
          <div className="text-sm text-gray-500">
            By: {review.customerName || customerName}
            {review.isVerifiedPurchase && (
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                ✓ Verified
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show review form for completed appointments
  if (appointmentStatus === 'completed') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Leave a Review</h3>
          {!showReviewForm && (
            <button
              onClick={() => setShowReviewForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Write Review
            </button>
          )}
        </div>

        {showReviewForm ? (
          <form onSubmit={handleSubmitReview} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rating *
              </label>
              {renderStars(formData.rating, true, (rating) => 
                setFormData(prev => ({ ...prev, rating }))
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Review Title (Optional)
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Summarize your experience..."
                maxLength={100}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Review (Optional)
              </label>
              <textarea
                value={formData.comment}
                onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Tell others about your experience..."
                maxLength={1000}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Display Name
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Your name"
                maxLength={100}
                required
              />
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                disabled={submitting}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {submitting ? 'Submitting...' : 'Submit Review'}
              </button>
              <button
                type="button"
                onClick={() => setShowReviewForm(false)}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        ) : (
          <p className="text-gray-600">
            Share your experience with {serviceName} to help other customers.
          </p>
        )}
      </div>
    );
  }

  // Don't show review section for non-completed appointments
  return null;
}
