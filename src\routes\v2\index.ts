import { Router } from 'express';
import authRoutes from './authRoutes';
import servicesRoutes from './servicesRoutes';
import appointmentsRoutes from './appointmentsRoutes';
import paymentConfirmationRoutes from './paymentConfirmationRoutes';
import userRoutes from './userRoutes';
import adminRoutes from './adminRoutes';
import dashboardRoutes from './dashboardRoutes';
import reviewRoutes from './reviewRoutes';
import availabilityRoutes from './availabilityRoutes';
import brandingRoutes from './brandingRoutes';

const router = Router();

// Mount v2 routes
router.use('/auth', authRoutes);
router.use('/services', servicesRoutes);
router.use('/appointments', appointmentsRoutes);
router.use('/payment-confirmations', paymentConfirmationRoutes);
router.use('/user', userRoutes);
router.use('/admin', adminRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/reviews', reviewRoutes);
router.use('/availability', availabilityRoutes);
router.use('/branding', brandingRoutes);

// V2 Health check endpoint
router.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: 'API v2 is running',
    version: '2.0.0',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      auth: '/api/v2/auth',
      services: '/api/v2/services',
      appointments: '/api/v2/appointments',
      paymentConfirmations: '/api/v2/payment-confirmations',
      user: '/api/v2/user',
      admin: '/api/v2/admin',
      dashboard: '/api/v2/dashboard',
      reviews: '/api/v2/reviews',
      availability: '/api/v2/availability',
      branding: '/api/v2/branding'
    }
  });
});

export default router;
