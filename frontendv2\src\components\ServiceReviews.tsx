import { useState, useEffect } from 'react';
import { reviewAPI, type ServiceReviewsResponse } from '../utils/reviewAPI';
import { useToast } from '../contexts/ToastContext';
import { API_CONFIG } from '../utils/config';

interface ServiceReviewsProps {
  serviceId: string;
  serviceName: string;
  allowReviewCreation?: boolean;
}

export default function ServiceReviews({ serviceId, serviceName, allowReviewCreation = true }: ServiceReviewsProps) {
  const [reviewsData, setReviewsData] = useState<ServiceReviewsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    comment: '',
    customerName: ''
  });
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    loadReviews(currentPage);
  }, [serviceId, currentPage]);

  const loadReviews = async (page: number) => {
    try {
      setLoading(true);
      setError(null);

      // Try to load reviews - this should work for guest users too
      const data = await reviewAPI.getServiceReviews(serviceId, page, 5);
      setReviewsData(data);
    } catch (error: any) {
      console.error('Error loading reviews:', error);

      // If API fails, show a fallback message but don't show error for guest users
      if (error.message?.includes('authentication') || error.message?.includes('unauthorized')) {
        // For guest users, just show that reviews are not available
        setReviewsData({
          reviews: [],
          pagination: { page: 1, limit: 5, total: 0, pages: 0 },
          stats: { averageRating: 0, totalReviews: 0, ratingDistribution: [] }
        });
      } else {
        setError(error.message || 'Failed to load reviews');
      }
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="rating-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${star <= rating ? '' : 'empty'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  const renderRatingDistribution = () => {
    if (!reviewsData?.stats.ratingDistribution.length) return null;

    const distribution = [1, 2, 3, 4, 5].map(rating => {
      const count = reviewsData.stats.ratingDistribution.filter(r => r === rating).length;
      const percentage = reviewsData.stats.totalReviews > 0 
        ? (count / reviewsData.stats.totalReviews) * 100 
        : 0;
      return { rating, count, percentage };
    }).reverse();

    return (
      <div>
        {distribution.map(({ rating, count, percentage }) => (
          <div key={rating} className="rating-bar">
            <span className="rating-bar-label">{rating} star{rating !== 1 ? 's' : ''}</span>
            <div className="rating-bar-track">
              <div
                className="rating-bar-fill"
                style={{ width: `${percentage}%` }}
              />
            </div>
            <span className="rating-bar-count">{count}</span>
          </div>
        ))}
      </div>
    );
  };

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.rating < 1 || formData.rating > 5) {
      showError('Please select a rating between 1 and 5 stars');
      return;
    }

    if (!formData.customerName.trim()) {
      showError('Please enter your name');
      return;
    }

    setSubmitting(true);
    try {
      // Create a general review for the service (not tied to a specific appointment)
      const reviewData = {
        serviceId: serviceId,
        rating: formData.rating,
        title: formData.title.trim() || undefined,
        comment: formData.comment.trim() || undefined,
        customerName: formData.customerName.trim()
      };

      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews/service-review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit review');
      }

      showSuccess('Thank you for your review! It will be reviewed by our team before being published.');
      setShowReviewForm(false);
      setFormData({
        rating: 5,
        title: '',
        comment: '',
        customerName: ''
      });

      // Reload reviews to show the new one (if approved)
      loadReviews(currentPage);
    } catch (error: any) {
      console.error('Error submitting review:', error);
      showError(error.message || 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStarsInteractive = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="interactive-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`interactive-star ${star <= rating ? 'filled' : ''}`}
            onClick={() => onRatingChange(star)}
          >
            ★
          </button>
        ))}
        <span className="rating-label">
          {rating} out of 5 stars
        </span>
      </div>
    );
  };

  if (loading && !reviewsData) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => loadReviews(currentPage)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!reviewsData || reviewsData.reviews.length === 0) {
    return (
      <div className="service-reviews-container">
        <div className="service-reviews-header">
          <h3 className="service-reviews-title">Customer Reviews</h3>
          {allowReviewCreation && !showReviewForm && (
            <button
              onClick={() => setShowReviewForm(true)}
              className="write-review-btn"
            >
              Write Review
            </button>
          )}
        </div>

        {showReviewForm ? (
          <div className="review-form-container">
            <h4 className="review-form-title">Write a Review</h4>
            <form onSubmit={handleSubmitReview}>
              <div className="form-group">
                <label className="form-label">
                  Rating *
                </label>
                {renderStarsInteractive(formData.rating, (rating) =>
                  setFormData(prev => ({ ...prev, rating }))
                )}
              </div>

              <div className="form-group">
                <label className="form-label">
                  Your Name *
                </label>
                <input
                  type="text"
                  value={formData.customerName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                  className="form-input"
                  placeholder="Enter your name"
                  maxLength={100}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  Review Title (Optional)
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="form-input"
                  placeholder="Summarize your experience..."
                  maxLength={100}
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  Your Review (Optional)
                </label>
                <textarea
                  value={formData.comment}
                  onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
                  rows={4}
                  className="form-input form-textarea"
                  placeholder="Tell others about your experience with this service..."
                  maxLength={1000}
                />
              </div>

              <div className="form-actions">
                <button
                  type="submit"
                  disabled={submitting}
                  className="btn-submit"
                >
                  {submitting ? 'Submitting...' : 'Submit Review'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowReviewForm(false)}
                  className="btn-cancel"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        ) : (
          <div className="no-reviews-state">
            <div className="no-reviews-icon">⭐</div>
            <p className="no-reviews-title">No reviews yet for {serviceName}</p>
            <p className="no-reviews-subtitle">
              Be the first to share your experience!
            </p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="service-reviews-container">
      <div className="service-reviews-header">
        <h3 className="service-reviews-title">Customer Reviews</h3>
        {allowReviewCreation && !showReviewForm && (
          <button
            onClick={() => setShowReviewForm(true)}
            className="write-review-btn"
          >
            Write Review
          </button>
        )}
      </div>
      
      {/* Review Summary */}
      <div className="reviews-summary">
        <div className="average-rating">
          <div className="rating-number">
            {reviewsData.stats.averageRating.toFixed(1)}
          </div>
          <div className="rating-stars">
            {renderStars(Math.round(reviewsData.stats.averageRating))}
          </div>
          <div className="rating-count">
            Based on {reviewsData.stats.totalReviews} review{reviewsData.stats.totalReviews !== 1 ? 's' : ''}
          </div>
        </div>

        <div className="rating-distribution">
          <h4 style={{ marginBottom: '1rem', fontWeight: '500', color: '#333' }}>Rating Distribution</h4>
          {renderRatingDistribution()}
        </div>
      </div>

      {/* Review Creation Form */}
      {allowReviewCreation && showReviewForm && (
        <div className="review-form-container">
          <h4 className="review-form-title">Write a Review</h4>
          <form onSubmit={handleSubmitReview}>
            <div className="form-group">
              <label className="form-label">
                Rating *
              </label>
              {renderStarsInteractive(formData.rating, (rating) =>
                setFormData(prev => ({ ...prev, rating }))
              )}
            </div>

            <div className="form-group">
              <label className="form-label">
                Your Name *
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                className="form-input"
                placeholder="Enter your name"
                maxLength={100}
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">
                Review Title (Optional)
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="form-input"
                placeholder="Summarize your experience..."
                maxLength={100}
              />
            </div>

            <div className="form-group">
              <label className="form-label">
                Your Review (Optional)
              </label>
              <textarea
                value={formData.comment}
                onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
                rows={4}
                className="form-input form-textarea"
                placeholder="Tell others about your experience with this service..."
                maxLength={1000}
              />
            </div>

            <div className="form-actions">
              <button
                type="submit"
                disabled={submitting}
                className="btn-submit"
              >
                {submitting ? 'Submitting...' : 'Submit Review'}
              </button>
              <button
                type="button"
                onClick={() => setShowReviewForm(false)}
                className="btn-cancel"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Individual Reviews */}
      <div className="space-y-6">
        {reviewsData.reviews.map((review) => (
          <div key={review._id} className="border-b border-gray-200 pb-6 last:border-b-0">
            <div className="flex items-start justify-between mb-3">
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  {renderStars(review.rating)}
                  {review.isVerifiedPurchase && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                      ✓ Verified
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600">
                  By {review.customerName || 'Anonymous'} • {new Date(review.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
            
            {review.title && (
              <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
            )}
            
            {review.comment && (
              <p className="text-gray-700 leading-relaxed">{review.comment}</p>
            )}
          </div>
        ))}
      </div>

      {/* Pagination */}
      {reviewsData.pagination.pages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-8">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span className="px-4 py-2 text-sm text-gray-600">
            Page {currentPage} of {reviewsData.pagination.pages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(reviewsData.pagination.pages, prev + 1))}
            disabled={currentPage === reviewsData.pagination.pages}
            className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
