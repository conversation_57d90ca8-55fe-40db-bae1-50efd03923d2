# Frontend v2 Environment Variables Example
# Copy this file to .env and update the values

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v2

# App Configuration
VITE_APP_TITLE=MicroLocs - Service Management
VITE_APP_DESCRIPTION=Professional service management and booking platform
VITE_APP_URL=http://localhost:5174

# Development Configuration
VITE_NODE_ENV=development

# Optional: Analytics (if needed)
# VITE_GOOGLE_ANALYTICS_ID=GA_XXXXXXXXX
# VITE_FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Optional: External APIs (if needed)
# VITE_MAPS_API_KEY=your_google_maps_api_key
