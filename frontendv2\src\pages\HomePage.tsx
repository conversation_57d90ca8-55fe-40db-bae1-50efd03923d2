import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import HeroSection from '../components/Home/HeroSection'
import AboutSection from '../components/Home/AboutSection'
import MicrolocksSection from '../components/Home/MicrolocksSection'
import PriceListSection from '../components/Home/PriceListSection'
import ConsultationSection from '../components/Home/ConsultationSection'
import PoliciesSection from '../components/Home/PoliciesSection'
import DisclaimerSection from '../components/Home/DisclaimerSection'
import { type User } from '../utils/api'

interface HomePageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function HomePage({ currentUser, onLogout }: HomePageProps) {
  const navigate = useNavigate();
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);

  const handleBookService = (service: any) => {
    // Navigate to booking flow with service data
    const searchParams = new URLSearchParams({
      service: JSON.stringify(service)
    });
    navigate(`/booking/datetime?${searchParams.toString()}`);
  };

  // Navigation handlers are handled by the Header component now

  return (
    <div className="app">
      <Header
        currentUser={currentUser}
        onLogout={onLogout}
      />

      <main className="main-content">
        <HeroSection />
        
        <div className="info-sections">
          <AboutSection />
          <PoliciesSection />
          <DisclaimerSection />
          <MicrolocksSection />
          <PriceListSection onBookService={handleBookService} />
        </div>

        <ConsultationSection
          onBookService={handleBookService}
          showDetails={showConsultationDetails}
          onToggleDetails={() => setShowConsultationDetails(!showConsultationDetails)}
        />
      </main>
    </div>
  )
}
