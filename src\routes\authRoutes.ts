import { Router } from 'express';
import { AuthController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  loginValidation,
  forgotPasswordValidation
} from '../utils/validation';

const router = Router();

// Note: Registration/signup functionality has been removed
// Users are created through admin interface or appointment booking

// POST /api/auth/login
router.post(
  '/login',
  validate(loginValidation),
  AuthController.login
);

// POST /api/auth/forgot-password
router.post(
  '/forgot-password',
  validate(forgotPasswordValidation),
  AuthController.forgotPassword
);

// POST /api/auth/reset-password
router.post(
  '/reset-password',
  AuthController.resetPassword
);

// GET /api/auth/verify
router.get(
  '/verify',
  authenticate,
  AuthController.verify
);

// POST /api/auth/logout
router.post(
  '/logout',
  authenticate,
  AuthController.logout
);

export default router;
