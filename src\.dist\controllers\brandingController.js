"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandingController = void 0;
const models_1 = require("../models");
const response_1 = require("../utils/response");
class BrandingController {
    /**
     * Get complete branding configuration in a single request
     * This returns ONLY database data - no hardcoded values
     */
    static async getCompleteBranding(req, res) {
        try {
            // Get branding content from database
            let branding = await models_1.Branding.findOne();
            if (!branding) {
                // Create default branding if none exists
                branding = await models_1.Branding.create({});
            }
            // Return ONLY database data - NO hardcoded values anywhere!
            const completeBranding = {
                // Flatten the branding data with all the fields from database ONLY
                branding: {
                    // Global/Site information - PURE DATABASE VALUES
                    siteName: branding.global?.siteName,
                    tagline: branding.global?.tagline,
                    phone: branding.global?.phone,
                    email: branding.global?.email,
                    address: branding.global?.address,
                    // Social media - PURE DATABASE VALUES
                    instagram: branding.global?.instagram,
                    facebook: branding.global?.facebook,
                    twitter: branding.global?.twitter,
                    youtube: branding.global?.youtube,
                    // Core images and branding - PURE DATABASE VALUES
                    logo: branding.global?.logo,
                    heroImage: branding.home?.heroImage,
                    favicon: branding.global?.favicon,
                    // Home page content - PURE DATABASE VALUES
                    heroTitle: branding.home?.heroTitle,
                    heroSubtitle: branding.home?.heroSubtitle,
                    aboutTitle: branding.home?.aboutTitle,
                    aboutText: branding.home?.aboutText,
                    testimonialHeading: branding.home?.testimonialHeading,
                    // Services content - PURE DATABASE VALUES
                    servicesTitle: branding.services?.pageTitle,
                    servicesSubtitle: branding.services?.pageSubtitle,
                    servicesDescription: branding.services?.pageDescription,
                    serviceLocMaintenance: branding.services?.serviceLocMaintenance,
                    serviceLocMaintenanceDesc: branding.services?.serviceLocMaintenanceDesc,
                    serviceStarterLocs: branding.services?.serviceStarterLocs,
                    serviceStarterLocsDesc: branding.services?.serviceStarterLocsDesc,
                    serviceLocStyling: branding.services?.serviceLocStyling,
                    serviceLocStylingDesc: branding.services?.serviceLocStylingDesc,
                    serviceNaturalHairCare: branding.services?.serviceNaturalHairCare,
                    serviceNaturalHairCareDesc: branding.services?.serviceNaturalHairCareDesc,
                    // Consultation content - PURE DATABASE VALUES
                    consultationTitle: branding.consultation?.pageTitle,
                    consultationSubtitle: branding.consultation?.pageSubtitle,
                    consultationDescription: branding.consultation?.pageDescription,
                    consultationFormTitle: branding.consultation?.formTitle,
                    consultationFormSubtitle: branding.consultation?.formSubtitle,
                    // Shop content - PURE DATABASE VALUES
                    shopTitle: branding.shop?.pageTitle,
                    shopSubtitle: branding.shop?.pageSubtitle,
                    shopDescription: branding.shop?.pageDescription,
                    shopFeaturedTitle: branding.shop?.featuredCollectionTitle,
                    // Dashboard content - PURE DATABASE VALUES
                    dashboardWelcome: branding.dashboard?.welcomeMessage,
                    dashboardOverviewTitle: branding.dashboard?.overviewTitle,
                    dashboardAppointmentsTitle: branding.dashboard?.appointmentsTitle,
                    dashboardOrdersTitle: branding.dashboard?.ordersTitle,
                    dashboardFavoritesTitle: branding.dashboard?.favoritesTitle,
                    dashboardProfileTitle: branding.dashboard?.profileTitle,
                    dashboardNextAppointment: branding.dashboard?.nextAppointment,
                    dashboardRecentOrders: branding.dashboard?.recentOrders,
                    dashboardLoyaltyTitle: branding.dashboard?.loyaltyTitle,
                    // Authentication content - PURE DATABASE VALUES
                    loginTitle: branding.login?.pageTitle,
                    loginSubtitle: branding.login?.pageSubtitle,
                    signInButton: branding.login?.signInButton,
                    signingInText: branding.login?.signingInText,
                    noAccountText: branding.login?.noAccountText,
                    signUpLink: branding.login?.signUpLink,
                    forgotPasswordLink: branding.login?.forgotPasswordLink,
                    // Note: Signup-related fields removed - signup functionality disabled
                    // Cart content - PURE DATABASE VALUES
                    cartTitle: branding.cart?.pageTitle,
                    cartEmptyMessage: branding.cart?.emptyCartMessage,
                    cartShippingMessage: branding.messages?.cartShipping,
                    freeShippingThreshold: branding.cart?.freeShippingThreshold,
                    shippingCalculated: branding.cart?.shippingCalculated,
                    // Product Detail content - PURE DATABASE VALUES
                    quantityLabel: branding.productDetail?.quantityLabel,
                    overviewTab: branding.productDetail?.overviewTab,
                    ingredientsTab: branding.productDetail?.ingredientsTab,
                    reviewsTab: branding.productDetail?.reviewsTab,
                    // Buttons - PURE DATABASE VALUES
                    bookNowButton: branding.buttons?.bookNow,
                    shopNowButton: branding.buttons?.shopNow,
                    learnMoreButton: branding.buttons?.learnMore,
                    viewAllButton: branding.buttons?.viewAll,
                    continueShoppingButton: branding.buttons?.continueShopping,
                    proceedToCheckoutButton: branding.buttons?.proceedToCheckout,
                    addToCartButton: branding.buttons?.addToCart,
                    scheduleConsultationButton: branding.buttons?.scheduleConsultation,
                    writeReviewButton: branding.buttons?.writeReview,
                    // Navigation - PURE DATABASE VALUES
                    navHome: branding.navigation?.home,
                    navServices: branding.navigation?.services,
                    navShop: branding.navigation?.shop,
                    navConsultation: branding.navigation?.consultation,
                    navLogin: branding.navigation?.login,
                    // navSignup: branding.navigation?.signup, // Removed - signup functionality disabled
                    navDashboard: branding.navigation?.dashboard,
                    // Footer - PURE DATABASE VALUES
                    footerDescription: branding.footer?.description,
                    footerQuickLinks: branding.footer?.quickLinks,
                    footerContact: branding.footer?.contact,
                    footerFollowUs: branding.footer?.followUs,
                    footerCopyright: branding.footer?.copyrightText,
                    // Testimonials - PURE DATABASE VALUES
                    testimonialsTitle: branding.testimonials?.title,
                    testimonialsSubtitle: branding.testimonials?.subtitle,
                    reviewsTitle: branding.reviews?.sectionTitle,
                    // Messages - PURE DATABASE VALUES
                    loadingMessage: branding.messages?.loading,
                    errorMessage: branding.messages?.error,
                    notFoundMessage: branding.messages?.notFound,
                    comingSoonMessage: branding.messages?.comingSoon
                },
                // Business profile from database - PURE DATABASE VALUES
                business: branding.business,
                // Theme settings from database - PURE DATABASE VALUES
                theme: branding.theme,
                // Site settings from database - PURE DATABASE VALUES
                site: branding.site
            };
            (0, response_1.sendSuccess)(res, 'Complete branding configuration retrieved successfully', completeBranding);
        }
        catch (error) {
            console.error('Get complete branding error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getBrandingContent(req, res) {
        try {
            let branding = await models_1.Branding.findOne();
            if (!branding) {
                // Create default branding if none exists
                branding = await models_1.Branding.create({});
            }
            (0, response_1.sendSuccess)(res, 'Branding content retrieved successfully', branding);
        }
        catch (error) {
            console.error('Get branding content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async getBusinessProfile(req, res) {
        try {
            // Get business profile from database ONLY - NO hardcoded values!
            const branding = await models_1.Branding.findOne();
            if (!branding) {
                (0, response_1.sendError)(res, 'No branding configuration found');
                return;
            }
            // Return ONLY database business data
            const businessProfile = branding.business;
            (0, response_1.sendSuccess)(res, 'Business profile retrieved successfully', businessProfile);
        }
        catch (error) {
            console.error('Error getting business profile:', error);
            (0, response_1.sendError)(res, 'Failed to get business profile');
        }
    }
    static async getThemeSettings(req, res) {
        try {
            // Get theme settings from database ONLY - NO hardcoded values!
            const branding = await models_1.Branding.findOne();
            if (!branding) {
                (0, response_1.sendError)(res, 'No branding configuration found');
                return;
            }
            // Return ONLY database theme data
            const themeSettings = branding.theme;
            (0, response_1.sendSuccess)(res, 'Theme settings retrieved successfully', themeSettings);
        }
        catch (error) {
            console.error('Error getting theme settings:', error);
            (0, response_1.sendError)(res, 'Failed to get theme settings');
        }
    }
    static async getSiteSettings(req, res) {
        try {
            // Get site settings from database ONLY - NO hardcoded values!
            const branding = await models_1.Branding.findOne();
            if (!branding) {
                (0, response_1.sendError)(res, 'No branding configuration found');
                return;
            }
            // Return ONLY database site data
            const siteSettings = branding.site;
            (0, response_1.sendSuccess)(res, 'Site settings retrieved successfully', siteSettings);
        }
        catch (error) {
            console.error('Error getting site settings:', error);
            (0, response_1.sendError)(res, 'Failed to get site settings');
        }
    }
    static async updateBrandingContent(req, res) {
        try {
            const updateData = req.body;
            let branding = await models_1.Branding.findOne();
            if (!branding) {
                branding = await models_1.Branding.create(updateData);
            }
            else {
                Object.assign(branding, updateData);
                // Synchronize global fields with business fields to avoid duplicates
                if (updateData.global) {
                    if (!branding.business) {
                        branding.business = {
                            name: '',
                            tagline: '',
                            description: '',
                            phone: '',
                            email: '',
                            address: {
                                street: '',
                                city: '',
                                state: '',
                                zip: '',
                                full: ''
                            },
                            social: {
                                instagram: '',
                                facebook: '',
                                twitter: ''
                            },
                            hours: {
                                monday: '',
                                tuesday: '',
                                wednesday: '',
                                thursday: '',
                                friday: '',
                                saturday: '',
                                sunday: ''
                            }
                        };
                    }
                    // Sync key fields between global and business sections
                    if (updateData.global.phone) {
                        branding.business.phone = updateData.global.phone;
                    }
                    if (updateData.global.email) {
                        branding.business.email = updateData.global.email;
                    }
                    if (updateData.global.siteName) {
                        branding.business.name = updateData.global.siteName;
                    }
                    if (updateData.global.tagline) {
                        branding.business.tagline = updateData.global.tagline;
                    }
                    if (updateData.global.address) {
                        if (!branding.business.address) {
                            branding.business.address = {
                                street: '',
                                city: '',
                                state: '',
                                zip: '',
                                full: ''
                            };
                        }
                        branding.business.address.full = updateData.global.address;
                    }
                    // Sync social media from global to business
                    if (updateData.global.instagram || updateData.global.facebook || updateData.global.twitter || updateData.global.youtube) {
                        if (!branding.business.social) {
                            branding.business.social = {
                                instagram: '',
                                facebook: '',
                                twitter: ''
                            };
                        }
                        if (updateData.global.instagram)
                            branding.business.social.instagram = updateData.global.instagram;
                        if (updateData.global.facebook)
                            branding.business.social.facebook = updateData.global.facebook;
                        if (updateData.global.twitter)
                            branding.business.social.twitter = updateData.global.twitter;
                    }
                }
                await branding.save();
            }
            (0, response_1.sendSuccess)(res, 'Branding content updated successfully', branding);
        }
        catch (error) {
            console.error('Update branding content error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async updateBrandingSection(req, res) {
        try {
            const { section } = req.params;
            const updateData = req.body;
            const validSections = [
                'global', 'home', 'services', 'shop', 'consultation',
                'login', 'signup', 'cart', 'productDetail', 'footer',
                'dashboard', 'buttons', 'navigation', 'testimonials',
                'reviews', 'contact', 'paymentConfirmation', 'messages',
                'business', 'theme', 'site'
            ];
            if (!validSections.includes(section)) {
                (0, response_1.sendError)(res, 'Invalid section specified');
                return;
            }
            let branding = await models_1.Branding.findOne();
            if (!branding) {
                branding = await models_1.Branding.create({});
            }
            // Update specific section
            branding[section] = { ...branding[section], ...updateData };
            await branding.save();
            (0, response_1.sendSuccess)(res, `${section} section updated successfully`, branding);
        }
        catch (error) {
            console.error('Update branding section error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.BrandingController = BrandingController;
