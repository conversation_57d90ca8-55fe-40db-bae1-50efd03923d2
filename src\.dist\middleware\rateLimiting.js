"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paymentRateLimit = exports.adminRateLimit = exports.uploadRateLimit = exports.apiRateLimit = exports.authRateLimit = exports.generalRateLimit = void 0;
const express_rate_limit_1 = require("express-rate-limit");
const production_1 = require("../config/production");
// General rate limiting
exports.generalRateLimit = (0, express_rate_limit_1.rateLimit)({
    ...production_1.productionConfig.security.rateLimit,
    keyGenerator: (req) => {
        // Use IP address as key, but consider X-Forwarded-For for proxies
        return req.ip || req.connection.remoteAddress || 'unknown';
    },
    handler: (req, res) => {
        res.status(429).json({
            success: false,
            message: 'Too many requests from this IP, please try again later.',
            retryAfter: Math.round(production_1.productionConfig.security.rateLimit.windowMs / 1000)
        });
    }
});
// Strict rate limiting for authentication endpoints
exports.authRateLimit = (0, express_rate_limit_1.rateLimit)({
    ...production_1.productionConfig.security.authRateLimit,
    keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress || 'unknown';
    },
    handler: (req, res) => {
        res.status(429).json({
            success: false,
            message: 'Too many authentication attempts, please try again later.',
            retryAfter: Math.round(production_1.productionConfig.security.authRateLimit.windowMs / 1000)
        });
    }
});
// API-specific rate limiting for different endpoints
exports.apiRateLimit = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Higher limit for general API usage
    message: 'API rate limit exceeded, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        // For authenticated users, use user ID + IP
        const userId = req.user?.id;
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        return userId ? `${userId}:${ip}` : ip;
    }
});
// File upload rate limiting
exports.uploadRateLimit = (0, express_rate_limit_1.rateLimit)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 50, // 50 uploads per hour
    message: 'Upload rate limit exceeded, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const userId = req.user?.id;
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        return userId ? `upload:${userId}:${ip}` : `upload:${ip}`;
    }
});
// Admin endpoints rate limiting
exports.adminRateLimit = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // Higher limit for admin operations
    message: 'Admin rate limit exceeded, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const userId = req.user?.id;
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        return `admin:${userId}:${ip}`;
    }
});
// Payment-related endpoints (very strict)
exports.paymentRateLimit = (0, express_rate_limit_1.rateLimit)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // Only 10 payment operations per hour
    message: 'Payment rate limit exceeded for security reasons.',
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const userId = req.user?.id;
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        return `payment:${userId}:${ip}`;
    }
});
