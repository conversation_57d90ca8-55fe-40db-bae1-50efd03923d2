import { useState } from 'react';
import Calendar from 'react-calendar';
import { format } from 'date-fns';
import 'react-calendar/dist/Calendar.css';
import './AdminCalendar.css';
import { API_CONFIG } from '../../utils/config';

interface Props {
  onDateSelect?: (date: Date) => void;
}

export default function AdminCalendar({ onDateSelect }: Props) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  // Fetch availability for a date
  const fetchAvailability = async (date: Date) => {
    try {
      const formattedDate = format(date, 'yyyy-MM-dd');
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/v2/admin/availability/time-slots/${formattedDate}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      const data = await response.json();
      if (data.success && data.timeSlots && data.timeSlots.length > 0) {
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error fetching availability:', err);
      return false;
    }
  };

  // Handle date selection
  const handleDateSelect = async (value: Date | Date[]) => {
    if (value instanceof Date) {
      setSelectedDate(value);
      const isAvailable = await fetchAvailability(value);
      if (isAvailable && onDateSelect) {
        onDateSelect(value);
      }
    }
  };

  return (
    <div className="calendar-section">
      <Calendar
        onChange={(value) => {
          if (value instanceof Date) {
            handleDateSelect(value);
          }
        }}
        value={selectedDate}
        minDate={new Date()}
        className="react-calendar"
        tileClassName={({ date }) => {
          if (date.toDateString() === selectedDate.toDateString()) {
            return 'selected-date';
          }
          return '';
        }}
      />
    </div>
  );
}
