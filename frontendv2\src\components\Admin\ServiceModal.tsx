import React, { useState, useEffect } from 'react';
import { type Service, type CreateServiceData } from '../../utils/serviceAPI';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (serviceData: CreateServiceData) => void;
  service?: Service | null;
  categories: string[];
}

interface FormData {
  name: string;
  description: string;
  category: string;
  duration: string;
  price: string;
  isActive: boolean;
  image?: string;
  images?: string[];
}

interface FormErrors {
  name?: string;
  description?: string;
  category?: string;
  duration?: string;
  price?: string;
}

export default function ServiceModal({ 
  isOpen, 
  onClose, 
  onSave, 
  service, 
  categories 
}: ServiceModalProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    category: '',
    duration: '',
    price: '',
    isActive: true,
    image: '',
    images: []
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);

  // Initialize form data when service changes
  useEffect(() => {
    if (service) {
      setFormData({
        name: service.name || '',
        description: service.description || '',
        category: service.category || '',
        duration: service.duration?.toString() || '',
        price: service.price?.toString() || '',
        isActive: service.isActive !== false,
        image: service.image || '',
        images: service.images || []
      });
    } else {
      setFormData({
        name: '',
        description: '',
        category: '',
        duration: '',
        price: '',
        isActive: true,
        image: '',
        images: []
      });
    }
    setErrors({});
    setNewCategory('');
    setShowNewCategoryInput(false);
  }, [service, isOpen]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.length > 100) {
      newErrors.name = 'Service name must be 100 characters or less';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length > 1000) {
      newErrors.description = 'Description must be 1000 characters or less';
    }

    const finalCategory = showNewCategoryInput ? newCategory : formData.category;
    if (!finalCategory.trim()) {
      newErrors.category = 'Category is required';
    } else if (finalCategory.length > 100) {
      newErrors.category = 'Category name must be 100 characters or less';
    }

    const duration = parseFloat(formData.duration);
    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    } else if (isNaN(duration) || duration <= 0) {
      newErrors.duration = 'Duration must be a positive number';
    }

    const price = parseFloat(formData.price);
    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(price) || price < 0) {
      newErrors.price = 'Price must be a non-negative number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const finalCategory = showNewCategoryInput ? newCategory.trim() : formData.category;
      
      const serviceData: CreateServiceData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        category: finalCategory,
        duration: parseFloat(formData.duration),
        price: parseFloat(formData.price),
        isActive: formData.isActive,
        image: formData.image || undefined,
        images: formData.images?.filter(img => img.trim()) || []
      };

      await onSave(serviceData);
    } catch (error) {
      console.error('Error saving service:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  // Handle category selection
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    
    if (value === '__new__') {
      setShowNewCategoryInput(true);
      setFormData(prev => ({ ...prev, category: '' }));
    } else {
      setShowNewCategoryInput(false);
      setFormData(prev => ({ ...prev, category: value }));
      setNewCategory('');
    }

    // Clear category error
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: undefined }));
    }
  };

  // Handle new category input
  const handleNewCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewCategory(e.target.value);
    
    // Clear category error
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: undefined }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-container modal-lg">
        {/* Header */}
        <div className="modal-header">
          <h2 className="modal-title">
            {service ? 'Edit Service' : 'Add New Service'}
          </h2>
          <button
            onClick={onClose}
            className="modal-close-button"
            disabled={isSubmitting}
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="modal-content">
          <form onSubmit={handleSubmit} className="appointment-form">
            {/* Basic Information */}
            <div className="form-section">
              <h4>Basic Information</h4>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">Service Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`form-input ${errors.name ? 'error' : ''}`}
                    placeholder="Enter service name"
                    disabled={isSubmitting}
                  />
                  {errors.name && <span className="error-text">{errors.name}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="category">Category *</label>
                  {!showNewCategoryInput ? (
                    <select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleCategoryChange}
                      className={`form-input ${errors.category ? 'error' : ''}`}
                      disabled={isSubmitting}
                    >
                      <option value="">Select a category</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                      <option value="__new__">+ Add New Category</option>
                    </select>
                  ) : (
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newCategory}
                        onChange={handleNewCategoryChange}
                        className={`form-input flex-1 ${errors.category ? 'error' : ''}`}
                        placeholder="Enter new category name"
                        disabled={isSubmitting}
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setShowNewCategoryInput(false);
                          setNewCategory('');
                        }}
                        className="btn btn-outline btn-sm"
                        disabled={isSubmitting}
                      >
                        Cancel
                      </button>
                    </div>
                  )}
                  {errors.category && <span className="error-text">{errors.category}</span>}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="description">Description *</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={4}
                  className={`form-textarea ${errors.description ? 'error' : ''}`}
                  placeholder="Describe the service in detail"
                  disabled={isSubmitting}
                />
                {errors.description && <span className="error-text">{errors.description}</span>}
                <small className="text-gray-500">
                  {formData.description.length}/1000 characters
                </small>
              </div>
            </div>

            {/* Pricing and Duration */}
            <div className="form-section">
              <h4>Pricing & Duration</h4>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="price">Price ($) *</label>
                  <input
                    type="number"
                    id="price"
                    name="price"
                    value={formData.price}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className={`form-input ${errors.price ? 'error' : ''}`}
                    placeholder="0.00"
                    disabled={isSubmitting}
                  />
                  {errors.price && <span className="error-text">{errors.price}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="duration">Duration (hours) *</label>
                  <input
                    type="number"
                    id="duration"
                    name="duration"
                    value={formData.duration}
                    onChange={handleChange}
                    min="0.25"
                    step="0.25"
                    className={`form-input ${errors.duration ? 'error' : ''}`}
                    placeholder="1.5"
                    disabled={isSubmitting}
                  />
                  {errors.duration && <span className="error-text">{errors.duration}</span>}
                  <small className="text-gray-500">
                    Duration in hours (e.g., 1.5 for 1 hour 30 minutes)
                  </small>
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="form-section">
              <h4>Status</h4>
              
              <div className="form-group">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleChange}
                    className="mr-2"
                    disabled={isSubmitting}
                  />
                  Active (visible to customers)
                </label>
              </div>
            </div>

            {/* Form Actions */}
            <div className="form-actions">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : service ? 'Update Service' : 'Create Service'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
