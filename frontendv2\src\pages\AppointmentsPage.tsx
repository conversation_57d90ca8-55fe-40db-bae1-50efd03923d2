import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import AppointmentReview from '../components/AppointmentReview'
import { appointmentAPI, type Appointment } from '../utils/appointmentAPI'
import { type User } from '../utils/api'

interface AppointmentsPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

// Using Appointment interface from appointmentAPI

export default function AppointmentsPage({ currentUser, onLogout }: AppointmentsPageProps) {
  const navigate = useNavigate();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled'>('all');

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }
    
    // TODO: Fetch appointments from backend
    fetchAppointments();
  }, [currentUser, navigate]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      let appointmentsData: Appointment[];
      if (currentUser?.role === 'admin') {
        appointmentsData = await appointmentAPI.getAppointments();
      } else {
        appointmentsData = await appointmentAPI.getUserAppointments();
      }

      setAppointments(appointmentsData);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setError('Failed to load appointments. Please try again.');
      setAppointments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (appointmentId: string, newStatus: Appointment['status']) => {
    try {
      await appointmentAPI.updateAppointmentStatus(appointmentId, newStatus);

      setAppointments(prev =>
        prev.map(apt =>
          apt.id === appointmentId ? { ...apt, status: newStatus } : apt
        )
      );
    } catch (error) {
      console.error('Error updating appointment status:', error);
      alert('Failed to update appointment status. Please try again.');
    }
  };

  const filteredAppointments = appointments.filter(apt => 
    filter === 'all' || apt.status === filter
  );

  const getStatusColor = (status: Appointment['status']) => {
    switch (status) {
      case 'pending': return '#f59e0b';
      case 'confirmed': return '#10b981';
      case 'completed': return '#6b7280';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading appointments...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button
              className="btn-primary"
              onClick={() => {
                setError(null);
                fetchAppointments();
              }}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Appointments</h1>
          <button 
            className="btn-primary"
            onClick={() => navigate('/')}
          >
            Book New Appointment
          </button>
        </div>

        <div className="filter-tabs">
          {(['all', 'pending', 'confirmed', 'completed', 'cancelled'] as const).map(status => (
            <button
              key={status}
              className={`filter-tab ${filter === status ? 'active' : ''}`}
              onClick={() => setFilter(status)}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </button>
          ))}
        </div>

        <div className="appointments-list">
          {filteredAppointments.length === 0 ? (
            <div className="empty-state">
              <p>No appointments found for the selected filter.</p>
            </div>
          ) : (
            filteredAppointments.map(appointment => (
              <div key={appointment.id} className="appointment-card">
                <div className="appointment-header">
                  <h3>{appointment.serviceName}</h3>
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(appointment.status) }}
                  >
                    {appointment.status}
                  </span>
                </div>
                
                <div className="appointment-details">
                  <p><strong>Date:</strong> {appointment.date}</p>
                  <p><strong>Time:</strong> {appointment.time}</p>
                  <p><strong>Total Price:</strong> ${appointment.totalPrice}</p>
                  <p><strong>Customer:</strong> {appointment.customerInfo.firstName} {appointment.customerInfo.lastName}</p>
                  <p><strong>Email:</strong> {appointment.customerInfo.email}</p>
                  {appointment.customerInfo.phone && (
                    <p><strong>Phone:</strong> {appointment.customerInfo.phone}</p>
                  )}
                  {appointment.addOns && appointment.addOns.length > 0 && (
                    <p><strong>Add-ons:</strong> {appointment.addOns.map(addon => addon.name).join(', ')}</p>
                  )}
                  <p><strong>Payment Method:</strong> {appointment.customerInfo.paymentMethod.toUpperCase()}</p>
                </div>

                {currentUser?.role === 'admin' && (
                  <div className="appointment-actions">
                    <button
                      onClick={() => handleStatusUpdate(appointment.id, 'confirmed')}
                      disabled={appointment.status === 'confirmed'}
                    >
                      Confirm
                    </button>
                    <button
                      onClick={() => handleStatusUpdate(appointment.id, 'completed')}
                      disabled={appointment.status === 'completed'}
                    >
                      Complete
                    </button>
                    <button
                      onClick={() => handleStatusUpdate(appointment.id, 'cancelled')}
                      disabled={appointment.status === 'cancelled'}
                    >
                      Cancel
                    </button>
                  </div>
                )}

                {/* Review Section */}
                <AppointmentReview
                  appointmentId={appointment.id}
                  appointmentStatus={appointment.status}
                  customerName={`${appointment.customerInfo.firstName} ${appointment.customerInfo.lastName}`}
                  serviceName={appointment.serviceName}
                  onReviewSubmitted={(review) => {
                    console.log('Review submitted:', review);
                    // Optionally refresh appointments or show success message
                  }}
                />
              </div>
            ))
          )}
        </div>
      </main>
    </div>
  )
}
