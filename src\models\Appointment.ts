import mongoose, { Schema } from 'mongoose';
import { IAppointment } from '../types';

const appointmentSchema = new Schema<IAppointment>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  service: {
    type: Schema.Types.ObjectId,
    ref: 'Service'
  },
  date: {
    type: Date
  },
  time: {
    type: String
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'completed', 'cancelled'],
    default: 'pending'
  },
  type: {
    type: String,
    enum: ['consultation', 'service'],
    default: 'consultation'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded'],
    default: 'pending'
  },
  customerInfo: {
    name: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      lowercase: true,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    }
  },
  message: {
    type: String,
    trim: true
  },
  totalPrice: {
    type: Number,
    default: 0
  },
  paymentProofs: [{
    id: String,
    amount: Number,
    paymentMethod: String,
    proofImage: String,
    status: {
      type: String,
      enum: ['pending', 'verified', 'rejected'],
      default: 'pending'
    },
    notes: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    verifiedAt: Date,
    verifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }]
}, {
  timestamps: true
});

// Keep only helpful indexes (no uniqueness)
appointmentSchema.index({ user: 1 });
appointmentSchema.index({ service: 1 });
appointmentSchema.index({ date: 1 });
appointmentSchema.index({ status: 1 });
appointmentSchema.index({ date: 1, time: 1 });

export const Appointment = mongoose.model<IAppointment>('Appointment', appointmentSchema);
