"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const controllers_1 = require("../../controllers");
const response_1 = require("../../utils/response");
const router = (0, express_1.Router)();
// POST /api/v2/payment-confirmations - Create payment confirmation with image upload
router.post('/', auth_1.authenticate, controllers_1.UploadController.cloudinaryUploadSingle('proofImage'), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { appointmentId, orderId, amount, paymentMethod, notes } = req.body;
        // Validate required fields
        if (!amount || !paymentMethod) {
            (0, response_1.sendError)(res, 'Amount and payment method are required', undefined, 400);
            return;
        }
        if (!appointmentId && !orderId) {
            (0, response_1.sendError)(res, 'Either appointment ID or order ID is required', undefined, 400);
            return;
        }
        // Check if file was uploaded
        if (!req.file) {
            (0, response_1.sendError)(res, 'Payment proof image is required', undefined, 400);
            return;
        }
        // Verify appointment or order exists
        if (appointmentId) {
            const appointment = await models_1.Appointment.findById(appointmentId);
            if (!appointment) {
                (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
                return;
            }
            // Check if user owns the appointment
            if (appointment.user.toString() !== req.user._id.toString()) {
                (0, response_1.sendError)(res, 'Unauthorized access to appointment', undefined, 403);
                return;
            }
        }
        if (orderId) {
            const order = await models_1.Order.findById(orderId);
            if (!order) {
                (0, response_1.sendError)(res, 'Order not found', undefined, 404);
                return;
            }
            // Check if user owns the order
            if (order.user.toString() !== req.user._id.toString()) {
                (0, response_1.sendError)(res, 'Unauthorized access to order', undefined, 403);
                return;
            }
        }
        // Create payment confirmation
        const paymentConfirmation = await models_1.PaymentConfirmation.create({
            user: req.user._id,
            appointment: appointmentId || undefined,
            order: orderId || undefined,
            amount: parseFloat(amount),
            paymentMethod: paymentMethod,
            proofImage: req.file.path, // Cloudinary URL
            notes: notes || '',
            status: 'pending'
        });
        // Also add the payment proof to the appointment's paymentProofs array
        if (appointmentId) {
            await models_1.Appointment.findByIdAndUpdate(appointmentId, {
                $push: {
                    paymentProofs: {
                        id: paymentConfirmation._id.toString(),
                        amount: parseFloat(amount),
                        paymentMethod: paymentMethod,
                        proofImage: req.file.path,
                        status: 'pending',
                        notes: notes || '',
                        createdAt: new Date()
                    }
                }
            });
        }
        // Populate related data for response
        await paymentConfirmation.populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ]);
        const responseData = {
            id: paymentConfirmation._id,
            appointmentId: paymentConfirmation.appointment?._id,
            orderId: paymentConfirmation.order?._id,
            amount: paymentConfirmation.amount,
            paymentMethod: paymentConfirmation.paymentMethod,
            proofImage: paymentConfirmation.proofImage,
            notes: paymentConfirmation.notes,
            status: paymentConfirmation.status,
            createdAt: paymentConfirmation.createdAt
        };
        (0, response_1.sendCreated)(res, 'Payment confirmation submitted successfully', responseData);
    }
    catch (error) {
        console.error('Create payment confirmation error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/payment-confirmations/my - Get user's payment confirmations
router.get('/my', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const paymentConfirmations = await models_1.PaymentConfirmation.find({ user: req.user._id })
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 });
        const formattedConfirmations = paymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            proofImage: confirmation.proofImage,
            notes: confirmation.notes,
            status: confirmation.status,
            verifiedAt: confirmation.verifiedAt,
            rejectionReason: confirmation.rejectionReason,
            createdAt: confirmation.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'Payment confirmations retrieved successfully', formattedConfirmations);
    }
    catch (error) {
        console.error('Get user payment confirmations error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/payment-confirmations/:id - Get payment confirmation by ID
router.get('/:id', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const paymentConfirmation = await models_1.PaymentConfirmation.findOne({
            _id: id,
            user: req.user._id
        }).populate([
            { path: 'appointment', populate: { path: 'service', select: 'name price' } },
            { path: 'order' }
        ]);
        if (!paymentConfirmation) {
            (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
            return;
        }
        const responseData = {
            id: paymentConfirmation._id,
            appointmentId: paymentConfirmation.appointment?._id,
            appointmentService: paymentConfirmation.appointment?.service?.name,
            appointmentServicePrice: paymentConfirmation.appointment?.service?.price,
            orderId: paymentConfirmation.order?._id,
            amount: paymentConfirmation.amount,
            paymentMethod: paymentConfirmation.paymentMethod,
            proofImage: paymentConfirmation.proofImage,
            notes: paymentConfirmation.notes,
            status: paymentConfirmation.status,
            verifiedAt: paymentConfirmation.verifiedAt,
            rejectionReason: paymentConfirmation.rejectionReason,
            createdAt: paymentConfirmation.createdAt
        };
        (0, response_1.sendSuccess)(res, 'Payment confirmation retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get payment confirmation error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/payment-confirmations/:id - Update payment confirmation
router.put('/:id', auth_1.authenticate, controllers_1.UploadController.cloudinaryUploadSingle('proofImage'), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const { amount, paymentMethod, notes } = req.body;
        const paymentConfirmation = await models_1.PaymentConfirmation.findOne({
            _id: id,
            user: req.user._id
        });
        if (!paymentConfirmation) {
            (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
            return;
        }
        // Only allow updates to pending confirmations
        if (paymentConfirmation.status !== 'pending') {
            (0, response_1.sendError)(res, 'Cannot update verified or rejected payment confirmations', undefined, 400);
            return;
        }
        // Update fields
        if (amount)
            paymentConfirmation.amount = parseFloat(amount);
        if (paymentMethod)
            paymentConfirmation.paymentMethod = paymentMethod;
        if (notes !== undefined)
            paymentConfirmation.notes = notes;
        if (req.file)
            paymentConfirmation.proofImage = req.file.path;
        await paymentConfirmation.save();
        const responseData = {
            id: paymentConfirmation._id,
            amount: paymentConfirmation.amount,
            paymentMethod: paymentConfirmation.paymentMethod,
            proofImage: paymentConfirmation.proofImage,
            notes: paymentConfirmation.notes,
            status: paymentConfirmation.status,
            updatedAt: paymentConfirmation.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Payment confirmation updated successfully', responseData);
    }
    catch (error) {
        console.error('Update payment confirmation error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/payment-confirmations/:id - Delete payment confirmation
router.delete('/:id', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const paymentConfirmation = await models_1.PaymentConfirmation.findOne({
            _id: id,
            user: req.user._id
        });
        if (!paymentConfirmation) {
            (0, response_1.sendError)(res, 'Payment confirmation not found', undefined, 404);
            return;
        }
        // Only allow deletion of pending confirmations
        if (paymentConfirmation.status !== 'pending') {
            (0, response_1.sendError)(res, 'Cannot delete verified or rejected payment confirmations', undefined, 400);
            return;
        }
        await models_1.PaymentConfirmation.findByIdAndDelete(id);
        (0, response_1.sendSuccess)(res, 'Payment confirmation deleted successfully', { id });
    }
    catch (error) {
        console.error('Delete payment confirmation error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
