import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { Review } from '../models/Review';

// Load environment variables
dotenv.config();

async function fixReviewIndexes() {
  try {
    console.log('🔧 Connecting to MongoDB...');

    // Connect to MongoDB using the same method as the main app
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocsbackend';
    await mongoose.connect(mongoUri);
    
    console.log('✅ Connected to MongoDB');
    
    // Get the collection
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('Database connection not established');
    }
    const collection = db.collection('reviews');
    
    // List existing indexes
    console.log('📋 Current indexes:');
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, index.key);
    });
    
    // Drop the problematic user_1_service_1 index
    try {
      console.log('🗑️ Dropping user_1_service_1 index...');
      await collection.dropIndex('user_1_service_1');
      console.log('✅ Successfully dropped user_1_service_1 index');
    } catch (error: any) {
      if (error.code === 27) {
        console.log('ℹ️ Index user_1_service_1 does not exist, skipping...');
      } else {
        console.error('❌ Error dropping index:', error.message);
      }
    }
    
    // Recreate indexes by calling ensureIndexes on the model
    console.log('🔄 Recreating indexes...');
    await Review.createIndexes();
    console.log('✅ Indexes recreated successfully');
    
    // List indexes again to confirm
    console.log('📋 Updated indexes:');
    const newIndexes = await collection.indexes();
    newIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}:`, index.key);
    });
    
    console.log('🎉 Index fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing indexes:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  fixReviewIndexes()
    .then(() => {
      console.log('✅ Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

export default fixReviewIndexes;
