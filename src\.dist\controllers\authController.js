"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const services_1 = require("../services");
const response_1 = require("../utils/response");
const models_1 = require("../models");
class AuthController {
    // Note: Registration functionality has been disabled
    // Users are created through admin interface or appointment booking
    /*
    static async register(req: Request, res: Response): Promise<void> {
      try {
        const { firstName, lastName, email, phone, password } = req.body;
  
        const result = await AuthService.register({
          firstName,
          lastName,
          name: `${firstName} ${lastName}`.trim(),
          email,
          phone,
          password
        });
  
        sendCreated(res, 'User registered successfully', {
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken
        });
      } catch (error) {
        console.error('Registration error:', error);
        sendError(res, (error as Error).message);
      }
    }
    */
    static async login(req, res) {
        try {
            const { email, password, rememberMe } = req.body;
            const result = await services_1.AuthService.login(email, password);
            (0, response_1.sendSuccess)(res, 'Login successful', {
                user: result.user,
                token: result.token,
                refreshToken: result.refreshToken,
                rememberMe: rememberMe || false
            });
        }
        catch (error) {
            console.error('Login error:', error);
            (0, response_1.sendError)(res, error.message, undefined, 401);
        }
    }
    static async forgotPassword(req, res) {
        try {
            const { email } = req.body;
            await services_1.AuthService.forgotPassword(email);
            (0, response_1.sendSuccess)(res, 'Password reset instructions sent to your email');
        }
        catch (error) {
            console.error('Forgot password error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async resetPassword(req, res) {
        try {
            const { token, password } = req.body;
            await services_1.AuthService.resetPassword(token, password);
            (0, response_1.sendSuccess)(res, 'Password reset successful');
        }
        catch (error) {
            console.error('Reset password error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async verify(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'User not found', undefined, 401);
                return;
            }
            console.log('Auth verify endpoint - User from middleware:', req.user._id);
            console.log('Auth verify endpoint - User ID type:', typeof req.user._id);
            const user = await services_1.AuthService.verifyToken(req.user._id);
            console.log('Auth verify endpoint - User returned:', user._id);
            console.log('Auth verify endpoint - Sending user to frontend');
            (0, response_1.sendSuccess)(res, 'Token verified successfully', { user });
        }
        catch (error) {
            console.error('Token verification error:', error);
            (0, response_1.sendError)(res, error.message, undefined, 401);
        }
    }
    static async logout(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            // Extract token from Authorization header
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                const token = authHeader.substring(7);
                // Add token to blacklist (implement token blacklist service)
                await services_1.AuthService.blacklistToken(token);
            }
            (0, response_1.sendSuccess)(res, 'Logout successful');
        }
        catch (error) {
            console.error('Logout error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async checkEmail(req, res) {
        try {
            const { email } = req.body;
            if (!email) {
                (0, response_1.sendError)(res, 'Email is required', undefined, 400);
                return;
            }
            const user = await models_1.User.findOne({ email: email.toLowerCase() });
            if (user) {
                // Determine if user is admin
                const isAdmin = user.role === 'admin';
                (0, response_1.sendSuccess)(res, 'Email found', {
                    exists: true,
                    isAdmin: isAdmin,
                    requiresPassword: isAdmin, // Admins require password, regular users can use email-only login
                    user: {
                        _id: user._id.toString(),
                        id: user._id.toString(), // Include both for compatibility
                        name: user.name,
                        firstName: user.firstName,
                        lastName: user.lastName,
                        phone: user.phone,
                        email: user.email,
                        role: user.role,
                        isVerified: user.isVerified,
                        notificationPreferences: user.notificationPreferences || {
                            email: true,
                            sms: false,
                            push: true
                        },
                        favorites: user.favorites || [],
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt
                    },
                    loginMethod: isAdmin ? 'password' : 'email-only',
                    message: isAdmin
                        ? 'Admin account found. Password required for login.'
                        : 'User account found. You can login with email only.',
                    authFlow: {
                        nextStep: isAdmin ? 'password-login' : 'email-only-login',
                        endpoint: isAdmin ? '/api/v2/auth/login' : '/api/v2/auth/login-email-only',
                        description: isAdmin
                            ? 'Use email and password to login'
                            : 'Use email-only login for quick access'
                    }
                });
            }
            else {
                (0, response_1.sendSuccess)(res, 'Email not found', {
                    exists: false,
                    isAdmin: false,
                    requiresPassword: false,
                    user: null,
                    loginMethod: 'contact-admin',
                    message: 'Email not found. Please contact support to get access to your account.'
                });
            }
        }
        catch (error) {
            console.error('Check email error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.AuthController = AuthController;
