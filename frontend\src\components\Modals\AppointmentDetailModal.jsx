import React, { useState, useEffect } from 'react'
import { FiX, FiEdit3, FiTrash2, FiUser, FiCalendar, FiClock, FiDollarSign, FiMessageSquare, FiSave, FiXCircle } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useToast } from '../../contexts/ToastContext'

const AppointmentDetailModal = ({
  appointment,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  services = [],
  customers = []
}) => {
  const { branding } = useBranding()
  const { showSuccess, showError } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editData, setEditData] = useState({})

  // Initialize edit data when appointment changes
  useEffect(() => {
    if (appointment) {
      setEditData({
        customerId: appointment.customerId || appointment.customerInfo?._id || '',
        serviceId: appointment.serviceId || appointment.service?._id || '',
        date: appointment.date ? appointment.date.split('T')[0] : '',
        time: appointment.time || '',
        status: appointment.status || 'pending',
        notes: appointment.notes || ''
      })
    }
  }, [appointment])

  if (!isOpen || !appointment) return null

  // Helper function to format duration from hours to readable format
  const formatDuration = (hours) => {
    if (!hours) return 'Duration not set'

    // Convert hours to a readable format
    if (hours === 1) {
      return '1 hour'
    } else if (hours % 1 === 0) {
      return `${hours} hours`
    } else {
      return `${hours} hours`
    }
  }

  // Helper function to format time from 24-hour to 12-hour format
  const formatTime = (time24) => {
    if (!time24) return 'Time not set'
    
    const timeParts = time24.split(':')
    if (timeParts.length < 2) return time24
    
    let hours = parseInt(timeParts[0])
    const minutes = timeParts[1]
    const ampm = hours >= 12 ? 'PM' : 'AM'
    
    hours = hours % 12
    hours = hours ? hours : 12
    
    return `${hours}:${minutes} ${ampm}`
  }

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this appointment?')) return

    setIsDeleting(true)
    try {
      await onDelete(appointment._id)
      showSuccess('Appointment deleted successfully')
      onClose()
    } catch (error) {
      showError('Failed to delete appointment')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    // Reset edit data to original appointment data
    setEditData({
      customerId: appointment.customerId || appointment.customerInfo?._id || '',
      serviceId: appointment.serviceId || appointment.service?._id || '',
      date: appointment.date ? appointment.date.split('T')[0] : '',
      time: appointment.time || '',
      status: appointment.status || 'pending',
      notes: appointment.notes || ''
    })
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await onEdit(appointment._id, editData)
      showSuccess('Appointment updated successfully')
      setIsEditing(false)
    } catch (error) {
      showError('Failed to update appointment')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200'
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: branding?.colors?.primary + '20' || '#3B82F620' }}
            >
              <FiCalendar className="w-5 h-5" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Appointment Details</h2>
              <p className="text-sm text-gray-500">
                ID: {appointment._id?.slice(-8) || 'N/A'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Badge */}
          <div className="flex justify-between items-start">
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full border ${getStatusColor(appointment.status)}`}>
              {appointment.status || 'Pending'}
            </span>
            <div className="flex space-x-2">
              {isEditing ? (
                <>
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    <span>{isSaving ? 'Saving...' : 'Save'}</span>
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
                  >
                    <FiXCircle className="w-4 h-4" />
                    <span>Cancel</span>
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleEdit}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
                  >
                    <FiEdit3 className="w-4 h-4" />
                    <span>Edit</span>
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
                  >
                    <FiTrash2 className="w-4 h-4" />
                    <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiUser className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Customer Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-600 mb-1">Customer</label>
                {isEditing ? (
                  <select
                    value={editData.customerId}
                    onChange={(e) => handleInputChange('customerId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a customer</option>
                    {customers.map(customer => (
                      <option key={customer._id} value={customer._id}>
                        {customer.name} - {customer.email}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="text-gray-900 font-medium">
                    {appointment.customerInfo?.name || appointment.user?.name || 'N/A'}
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
                <div className="text-gray-900">
                  {appointment.customerInfo?.email || appointment.user?.email || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Phone</label>
                <div className="text-gray-900">
                  {appointment.customerInfo?.phone || appointment.user?.phone || 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Service Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Service</label>
                {isEditing ? (
                  <select
                    value={editData.serviceId}
                    onChange={(e) => handleInputChange('serviceId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a service</option>
                    {services.map(service => (
                      <option key={service._id} value={service._id}>
                        {service.name} - ${service.price}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="text-gray-900 font-medium">
                    {appointment.service?.name || appointment.service || 'N/A'}
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Duration</label>
                <div className="text-gray-900">
                  {formatDuration(appointment.service?.duration)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Price</label>
                <div className="text-gray-900 font-medium flex items-center">
                  <FiDollarSign className="w-4 h-4 mr-1" />
                  {appointment.service?.price ? `${appointment.service.price}` : 'N/A'}
                </div>
              </div>
              {appointment.service?.description && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Description</label>
                  <div className="text-gray-900">
                    {appointment.service.description}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Appointment Details */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiClock className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Appointment Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Date</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={editData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                ) : (
                  <div className="text-gray-900 font-medium">
                    {appointment.date ? new Date(appointment.date).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) : 'N/A'}
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Time</label>
                {isEditing ? (
                  <input
                    type="time"
                    value={editData.time}
                    onChange={(e) => handleInputChange('time', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                ) : (
                  <div className="text-gray-900 font-medium">
                    {formatTime(appointment.time)}
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Created</label>
                <div className="text-gray-900">
                  {appointment.createdAt ? new Date(appointment.createdAt).toLocaleDateString() : 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                {isEditing ? (
                  <select
                    value={editData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="no-show">No Show</option>
                  </select>
                ) : (
                  <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full border ${getStatusColor(appointment.status)}`}>
                    {appointment.status || 'Pending'}
                  </span>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <div className="text-gray-900">
                  {appointment.updatedAt ? new Date(appointment.updatedAt).toLocaleDateString() : 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiMessageSquare className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Notes
            </h3>
            {isEditing ? (
              <textarea
                value={editData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Add notes about this appointment..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            ) : (
              <div className="text-gray-900 bg-white p-3 rounded border">
                {appointment.message || appointment.notes || 'No notes available'}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default AppointmentDetailModal
