import mongoose, { Schema, Document } from 'mongoose';

export interface ITimeSlot {
  time: string;
  isAvailable: boolean;
  reason?: string;
}

export interface IAdminAvailability extends Document {
  date: Date;
  timeSlots: ITimeSlot[];
  isFullDayUnavailable: boolean;
  reason?: string;
  createdBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export interface IGlobalAvailabilitySettings extends Document {
  defaultTimeSlots: ITimeSlot[];
  businessHours: {
    start: string;
    end: string;
  };
  workingDays: number[]; // 0-6 (Sunday-Saturday)
  slotDuration: number; // in minutes
  isGloballyUnavailable: boolean;
  globalUnavailabilityReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

const TimeSlotSchema = new Schema({
  time: {
    type: String,
    required: true,
    validate: {
      validator: function(v: string) {
        return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
      },
      message: 'Time must be in HH:MM format'
    }
  },
  isAvailable: {
    type: Boolean,
    default: true
  },
  reason: {
    type: String,
    maxlength: 200
  }
}, { _id: false });

const AdminAvailabilitySchema: Schema = new Schema(
  {
    date: {
      type: Date,
      required: true,
      index: true,
      validate: {
        validator: function(v: Date) {
          return v >= new Date(new Date().setHours(0, 0, 0, 0));
        },
        message: 'Date cannot be in the past'
      }
    },
    timeSlots: [TimeSlotSchema],
    isFullDayUnavailable: {
      type: Boolean,
      default: false
    },
    reason: {
      type: String,
      maxlength: 500
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true
  }
);

const GlobalAvailabilitySettingsSchema: Schema = new Schema(
  {
    defaultTimeSlots: [TimeSlotSchema],
    businessHours: {
      start: {
        type: String,
        required: true,
        default: '09:00',
        validate: {
          validator: function(v: string) {
            return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
          },
          message: 'Start time must be in HH:MM format'
        }
      },
      end: {
        type: String,
        required: true,
        default: '17:00',
        validate: {
          validator: function(v: string) {
            return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
          },
          message: 'End time must be in HH:MM format'
        }
      }
    },
    workingDays: {
      type: [Number],
      default: [1, 2, 3, 4, 5], // Monday to Friday
      validate: {
        validator: function(v: number[]) {
          return v.every(day => day >= 0 && day <= 6);
        },
        message: 'Working days must be between 0 (Sunday) and 6 (Saturday)'
      }
    },
    slotDuration: {
      type: Number,
      default: 30,
      min: 15,
      max: 120
    },
    isGloballyUnavailable: {
      type: Boolean,
      default: false
    },
    globalUnavailabilityReason: {
      type: String,
      maxlength: 500
    }
  },
  {
    timestamps: true
  }
);

// Ensure unique date entries
AdminAvailabilitySchema.index({ date: 1 }, { unique: true });

// Ensure only one global settings document
GlobalAvailabilitySettingsSchema.index({}, { unique: true });

// Static methods for AdminAvailability
AdminAvailabilitySchema.statics.generateDefaultTimeSlots = function(businessHours: { start: string; end: string }, slotDuration: number = 30): ITimeSlot[] {
  const slots: ITimeSlot[] = [];
  const [startHour, startMinute] = businessHours.start.split(':').map(Number);
  const [endHour, endMinute] = businessHours.end.split(':').map(Number);

  const startTime = startHour * 60 + startMinute;
  const endTime = endHour * 60 + endMinute;

  for (let time = startTime; time < endTime; time += slotDuration) {
    const hour = Math.floor(time / 60);
    const minute = time % 60;
    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

    slots.push({
      time: timeString,
      isAvailable: true
    });
  }

  return slots;
};

export const AdminAvailability = mongoose.model<IAdminAvailability>('AdminAvailability', AdminAvailabilitySchema);
export const GlobalAvailabilitySettings = mongoose.model<IGlobalAvailabilitySettings>('GlobalAvailabilitySettings', GlobalAvailabilitySettingsSchema);

export default AdminAvailability;
