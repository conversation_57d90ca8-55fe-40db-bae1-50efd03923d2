import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Layout/Header';
import { useToast } from '../contexts/ToastContext';
import { type User, getCurrentUser, authAPI } from '../utils/api';
import { API_CONFIG } from '../utils/config';

interface Service {
  _id: string;
  name: string;
  category: string;
  description?: string;
  price: number;
  duration: number;
}

interface Appointment {
  _id: string;
  service: Service;
  date: string;
  time: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  createdAt: string;
}

interface Review {
  _id: string;
  service: Service;
  appointment?: Appointment;
  rating: number;
  title?: string;
  comment?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  adminResponse?: string;
}

export default function ReviewsPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [completedAppointments, setCompletedAppointments] = useState<Appointment[]>([]);
  const [userReviews, setUserReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    rating: 5,
    title: '',
    comment: ''
  });

  // Guest login states
  const [showGuestLogin, setShowGuestLogin] = useState(false);
  const [guestEmail, setGuestEmail] = useState('');
  const [guestLoading, setGuestLoading] = useState(false);

  const navigate = useNavigate();
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    const initializeUser = async () => {
      try {
        const user = await getCurrentUser();
        if (user && user._id) {
          setCurrentUser(user);
          await loadUserData(user._id);
        } else {
          // Don't redirect immediately, show guest login option
          setLoading(false);
        }
      } catch (error) {
        console.error('Error getting current user:', error);
        setLoading(false);
      }
    };

    initializeUser();
  }, [navigate]);

  // Handle guest login with email only
  const handleGuestLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!guestEmail.trim()) {
      showError('Please enter your email address');
      return;
    }

    setGuestLoading(true);
    try {
      // First check if user exists
      const checkResponse = await authAPI.checkEmail(guestEmail);

      if (checkResponse.success && checkResponse.data?.exists && !checkResponse.data?.isAdmin) {
        // User exists and is not admin, login with email only
        const loginResponse = await authAPI.loginEmailOnly(guestEmail);

        if (loginResponse.success && loginResponse.data?.user) {
          setCurrentUser(loginResponse.data.user);
          if (loginResponse.data.user._id) {
            await loadUserData(loginResponse.data.user._id);
          } else {
            showError('User ID not found. Please contact support.');
          }
          setShowGuestLogin(false);
          showSuccess('Welcome back! You can now view and create reviews.');
        } else {
          showError('Failed to login. Please try again.');
        }
      } else if (checkResponse.data?.isAdmin) {
        showError('Admin users must use the full login page with password.');
      } else {
        showError('No account found with this email. Please book an appointment first to create an account.');
      }
    } catch (error) {
      console.error('Guest login error:', error);
      showError('Failed to login. Please try again.');
    } finally {
      setGuestLoading(false);
    }
  };

  const loadUserData = async (userId: string) => {
    try {
      setLoading(true);
      
      // Load completed appointments that can be reviewed
      const appointmentsResponse = await fetch(`${API_CONFIG.BASE_URL}/appointments/user/${userId}?status=completed`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (appointmentsResponse.ok) {
        const appointmentsData = await appointmentsResponse.json();
        setCompletedAppointments(appointmentsData.data || []);
      }

      // Load user's existing reviews
      const reviewsResponse = await fetch(`${API_CONFIG.BASE_URL}/reviews/user/${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (reviewsResponse.ok) {
        const reviewsData = await reviewsResponse.json();
        setUserReviews(reviewsData.data || []);
      }

    } catch (error: any) {
      console.error('Error loading user data:', error);
      showError('Failed to load your data');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReview = async (appointmentId: string) => {
    if (formData.rating < 1 || formData.rating > 5) {
      showError('Please select a rating between 1 and 5 stars');
      return;
    }

    setSubmitting(true);
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({
          appointmentId,
          rating: formData.rating,
          title: formData.title.trim() || undefined,
          comment: formData.comment.trim() || undefined
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit review');
      }

      showSuccess('Thank you for your review! It will be reviewed by our team before being published.');
      setShowReviewForm(null);
      setFormData({ rating: 5, title: '', comment: '' });
      
      // Reload user data to show the new review
      if (currentUser && currentUser._id) {
        await loadUserData(currentUser._id);
      }
    } catch (error: any) {
      console.error('Error submitting review:', error);
      showError(error.message || 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="rating-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${star <= rating ? '' : 'empty'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  const renderInteractiveStars = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="interactive-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`interactive-star ${star <= rating ? 'filled' : ''}`}
            onClick={() => onRatingChange(star)}
          >
            ★
          </button>
        ))}
        <span className="rating-label">
          {rating} out of 5 stars
        </span>
      </div>
    );
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'status-badge approved';
      case 'rejected':
        return 'status-badge rejected';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  const getAppointmentsToReview = () => {
    const reviewedAppointmentIds = userReviews.map(review => review.appointment?._id).filter(Boolean);
    return completedAppointments.filter(apt => !reviewedAppointmentIds.includes(apt._id));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header currentUser={currentUser} onLogout={() => {
          localStorage.removeItem('authToken');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
        }} />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show guest login if user is not authenticated
  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header currentUser={currentUser} onLogout={() => {
          localStorage.removeItem('authToken');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
        }} />

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="dashboard-header">
            <h1 className="dashboard-title">My Reviews</h1>
            <p className="dashboard-subtitle">Share your experience and view your feedback</p>
          </div>

          <div className="guest-login-section">
            <div className="guest-login-card">
              <div className="guest-login-header">
                <h2>Access Your Reviews</h2>
                <p>Enter your email to view and manage your reviews</p>
              </div>

              {!showGuestLogin ? (
                <div className="guest-login-options">
                  <button
                    onClick={() => setShowGuestLogin(true)}
                    className="guest-login-btn primary"
                  >
                    Login with Email
                  </button>
                  <button
                    onClick={() => navigate('/login')}
                    className="guest-login-btn secondary"
                  >
                    Full Login
                  </button>
                </div>
              ) : (
                <form onSubmit={handleGuestLogin} className="guest-login-form">
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      value={guestEmail}
                      onChange={(e) => setGuestEmail(e.target.value)}
                      className="form-input"
                      placeholder="Enter your email address"
                      required
                    />
                    <small className="form-help">
                      We'll find your account and reviews using your email address
                    </small>
                  </div>

                  <div className="form-actions">
                    <button
                      type="submit"
                      disabled={guestLoading}
                      className="btn-submit"
                    >
                      {guestLoading ? 'Checking...' : 'Access My Reviews'}
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowGuestLogin(false)}
                      className="btn-cancel"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              )}

              <div className="guest-login-footer">
                <p>
                  Don't have an account? Book an appointment to automatically create one!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header currentUser={currentUser} onLogout={() => {
        localStorage.removeItem('authToken');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login');
      }} />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="dashboard-header">
          <h1 className="dashboard-title">My Reviews</h1>
          <p className="dashboard-subtitle">Share your experience and view your feedback</p>
        </div>

        {/* Appointments to Review */}
        {getAppointmentsToReview().length > 0 && (
          <div className="dashboard-section">
            <h2 className="section-title">Leave a Review</h2>
            <p className="section-subtitle">Rate your recent completed appointments</p>
            
            <div className="appointments-grid">
              {getAppointmentsToReview().map((appointment) => (
                <div key={appointment._id} className="appointment-card">
                  <div className="appointment-header">
                    <h3 className="appointment-service">{appointment.service.name}</h3>
                    <span className="appointment-date">
                      {new Date(appointment.date).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="appointment-details">
                    <p className="service-category">{appointment.service.category}</p>
                    <p className="appointment-time">Time: {appointment.time}</p>
                  </div>

                  {showReviewForm === appointment._id ? (
                    <div className="review-form-container">
                      <h4 className="review-form-title">Write Your Review</h4>
                      <form onSubmit={(e) => {
                        e.preventDefault();
                        handleSubmitReview(appointment._id);
                      }}>
                        <div className="form-group">
                          <label className="form-label">Rating *</label>
                          {renderInteractiveStars(formData.rating, (rating) => 
                            setFormData(prev => ({ ...prev, rating }))
                          )}
                        </div>

                        <div className="form-group">
                          <label className="form-label">Review Title (Optional)</label>
                          <input
                            type="text"
                            value={formData.title}
                            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                            className="form-input"
                            placeholder="Summarize your experience..."
                            maxLength={100}
                          />
                        </div>

                        <div className="form-group">
                          <label className="form-label">Your Review (Optional)</label>
                          <textarea
                            value={formData.comment}
                            onChange={(e) => setFormData(prev => ({ ...prev, comment: e.target.value }))}
                            rows={4}
                            className="form-input form-textarea"
                            placeholder="Tell others about your experience..."
                            maxLength={1000}
                          />
                        </div>

                        <div className="form-actions">
                          <button
                            type="submit"
                            disabled={submitting}
                            className="btn-submit"
                          >
                            {submitting ? 'Submitting...' : 'Submit Review'}
                          </button>
                          <button
                            type="button"
                            onClick={() => setShowReviewForm(null)}
                            className="btn-cancel"
                          >
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  ) : (
                    <div className="appointment-actions">
                      <button
                        onClick={() => setShowReviewForm(appointment._id)}
                        className="write-review-btn"
                      >
                        Write Review
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User's Reviews */}
        {userReviews.length > 0 && (
          <div className="dashboard-section">
            <h2 className="section-title">Your Reviews</h2>
            <p className="section-subtitle">Reviews you've submitted</p>
            
            <div className="reviews-grid">
              {userReviews.map((review) => (
                <div key={review._id} className="review-card">
                  <div className="review-header">
                    <div className="review-service-info">
                      <h3 className="review-service-name">{review.service.name}</h3>
                      <span className="review-date">
                        {new Date(review.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="review-status">
                      {renderStars(review.rating)}
                      <span className={getStatusBadgeClass(review.status)}>
                        {review.status}
                      </span>
                    </div>
                  </div>

                  {review.title && (
                    <h4 className="review-title">{review.title}</h4>
                  )}
                  
                  {review.comment && (
                    <p className="review-comment">{review.comment}</p>
                  )}

                  {review.adminResponse && (
                    <div className="admin-response">
                      <div className="admin-response-label">Business Response:</div>
                      <div className="admin-response-text">{review.adminResponse}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {getAppointmentsToReview().length === 0 && userReviews.length === 0 && (
          <div className="empty-state">
            <div className="empty-state-icon">⭐</div>
            <h3 className="empty-state-title">No Reviews Yet</h3>
            <p className="empty-state-subtitle">
              Complete an appointment to leave your first review!
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
