"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const response_1 = require("../../utils/response");
const router = (0, express_1.Router)();
// GET /api/v2/user/profile - Get user profile
router.get('/profile', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const user = await models_1.User.findById(req.user._id).select('-password');
        if (!user) {
            (0, response_1.sendError)(res, 'User not found', undefined, 404);
            return;
        }
        const profileData = {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: user.role,
            isVerified: user.isVerified,
            notificationPreferences: user.notificationPreferences,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'User profile retrieved successfully', profileData);
    }
    catch (error) {
        console.error('Get user profile error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/user/profile - Update user profile
router.put('/profile', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { firstName, lastName, phone, notificationPreferences } = req.body;
        const user = await models_1.User.findById(req.user._id);
        if (!user) {
            (0, response_1.sendError)(res, 'User not found', undefined, 404);
            return;
        }
        // Update fields
        if (firstName)
            user.firstName = firstName;
        if (lastName)
            user.lastName = lastName;
        if (phone)
            user.phone = phone;
        if (notificationPreferences) {
            user.notificationPreferences = {
                ...user.notificationPreferences,
                ...notificationPreferences
            };
        }
        await user.save();
        const profileData = {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: user.role,
            isVerified: user.isVerified,
            notificationPreferences: user.notificationPreferences,
            updatedAt: user.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'User profile updated successfully', profileData);
    }
    catch (error) {
        console.error('Update user profile error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/user/dashboard - Get user dashboard data
router.get('/dashboard', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        // Get user's recent appointments
        const recentAppointments = await models_1.Appointment.find({ user: req.user._id })
            .populate('service', 'name price duration category')
            .sort({ date: -1, time: -1 })
            .limit(5);
        // Get user's payment confirmations
        const paymentConfirmations = await models_1.PaymentConfirmation.find({ user: req.user._id })
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .limit(5);
        // Get user's reviews
        const userReviews = await models_1.Review.find({ user: req.user._id })
            .populate('service', 'name category')
            .populate({
            path: 'appointment',
            select: 'date time service',
            populate: {
                path: 'service',
                select: 'name category'
            }
        })
            .sort({ createdAt: -1 })
            .limit(10);
        // Get completed appointments that can be reviewed (not already reviewed)
        const reviewedAppointmentIds = userReviews
            .filter(review => review.appointment)
            .map(review => review.appointment._id);
        const completedAppointments = await models_1.Appointment.find({
            user: req.user._id,
            status: 'completed',
            _id: { $nin: reviewedAppointmentIds }
        })
            .populate('service', 'name category price')
            .sort({ date: -1 })
            .limit(5);
        // Get appointment statistics
        const appointmentStats = await models_1.Appointment.aggregate([
            { $match: { user: req.user._id } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get payment confirmation statistics
        const paymentStats = await models_1.PaymentConfirmation.aggregate([
            { $match: { user: req.user._id } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalAmount: { $sum: '$amount' }
                }
            }
        ]);
        // Get review statistics
        const reviewStats = await models_1.Review.aggregate([
            { $match: { user: req.user._id } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    avgRating: { $avg: '$rating' }
                }
            }
        ]);
        // Format appointments
        const formattedAppointments = recentAppointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price,
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        // Format payment confirmations
        const formattedPaymentConfirmations = paymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            status: confirmation.status,
            createdAt: confirmation.createdAt
        }));
        // Format user reviews
        const formattedReviews = userReviews.map(review => {
            // Get service data from either direct service field or appointment.service
            const serviceData = review.service || review.appointment?.service;
            return {
                id: review._id,
                appointmentId: review.appointment?._id,
                serviceId: serviceData?._id,
                serviceName: serviceData?.name,
                serviceCategory: serviceData?.category,
                appointmentDate: review.appointment?.date,
                appointmentTime: review.appointment?.time,
                rating: review.rating,
                title: review.title,
                comment: review.comment,
                status: review.status,
                isVerifiedPurchase: review.isVerifiedPurchase,
                adminResponse: review.adminResponse,
                adminResponseDate: review.adminResponseDate,
                createdAt: review.createdAt,
                updatedAt: review.updatedAt
            };
        });
        // Format completed appointments available for review
        const formattedCompletedAppointments = completedAppointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            serviceCategory: appointment.service.category,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        // Format statistics
        const statsData = {
            appointments: appointmentStats.reduce((acc, stat) => {
                acc[stat._id] = stat.count;
                return acc;
            }, {}),
            payments: paymentStats.reduce((acc, stat) => {
                acc[stat._id] = {
                    count: stat.count,
                    totalAmount: stat.totalAmount
                };
                return acc;
            }, {}),
            reviews: {
                total: userReviews.length,
                byStatus: reviewStats.reduce((acc, stat) => {
                    acc[stat._id] = {
                        count: stat.count,
                        avgRating: stat.avgRating
                    };
                    return acc;
                }, {}),
                averageRating: userReviews.length > 0
                    ? userReviews.reduce((sum, review) => sum + review.rating, 0) / userReviews.length
                    : 0,
                pendingReviews: formattedCompletedAppointments.length
            }
        };
        const dashboardData = {
            user: {
                id: req.user._id,
                firstName: req.user.firstName,
                lastName: req.user.lastName,
                email: req.user.email
            },
            recentAppointments: formattedAppointments,
            recentPaymentConfirmations: formattedPaymentConfirmations,
            userReviews: formattedReviews,
            completedAppointmentsToReview: formattedCompletedAppointments,
            statistics: statsData
        };
        (0, response_1.sendSuccess)(res, 'Dashboard data retrieved successfully', dashboardData);
    }
    catch (error) {
        console.error('Get dashboard data error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/user/reviews - Get user's reviews with pagination
router.get('/reviews', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const query = { user: req.user._id };
        if (status && status !== 'all') {
            query.status = status;
        }
        const skip = (page - 1) * limit;
        const [reviews, total] = await Promise.all([
            models_1.Review.find(query)
                .populate('service', 'name category')
                .populate({
                path: 'appointment',
                select: 'date time service',
                populate: {
                    path: 'service',
                    select: 'name category'
                }
            })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            models_1.Review.countDocuments(query)
        ]);
        const formattedReviews = reviews.map(review => {
            // Get service data from either direct service field or appointment.service
            const serviceData = review.service || review.appointment?.service;
            return {
                id: review._id,
                appointmentId: review.appointment?._id,
                serviceId: serviceData?._id,
                serviceName: serviceData?.name,
                serviceCategory: serviceData?.category,
                appointmentDate: review.appointment?.date,
                appointmentTime: review.appointment?.time,
                rating: review.rating,
                title: review.title,
                comment: review.comment,
                status: review.status,
                isVerifiedPurchase: review.isVerifiedPurchase,
                adminResponse: review.adminResponse,
                adminResponseDate: review.adminResponseDate,
                createdAt: review.createdAt,
                updatedAt: review.updatedAt
            };
        });
        const responseData = {
            reviews: formattedReviews,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
                hasMore: page * limit < total
            }
        };
        (0, response_1.sendSuccess)(res, 'User reviews retrieved successfully', responseData);
    }
    catch (error) {
        console.error('Get user reviews error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/user/appointments-to-review - Get completed appointments that can be reviewed
router.get('/appointments-to-review', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        // Get appointments that have been reviewed
        const reviewedAppointments = await models_1.Review.find({
            user: req.user._id,
            appointment: { $exists: true }
        }).select('appointment');
        const reviewedAppointmentIds = reviewedAppointments.map(review => review.appointment);
        // Get completed appointments that haven't been reviewed
        const completedAppointments = await models_1.Appointment.find({
            user: req.user._id,
            status: 'completed',
            _id: { $nin: reviewedAppointmentIds }
        })
            .populate('service', 'name category price duration')
            .sort({ date: -1 });
        const formattedAppointments = completedAppointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            serviceCategory: appointment.service.category,
            servicePrice: appointment.service.price,
            serviceDuration: appointment.service.duration,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'Appointments to review retrieved successfully', formattedAppointments);
    }
    catch (error) {
        console.error('Get appointments to review error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/user/appointments - Get all user appointments with pagination
router.get('/appointments', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const query = { user: req.user._id };
        if (status) {
            query.status = status;
        }
        const appointments = await models_1.Appointment.find(query)
            .populate('service', 'name price duration category')
            .sort({ date: -1, time: -1 })
            .skip((page - 1) * limit)
            .limit(limit);
        const total = await models_1.Appointment.countDocuments(query);
        const formattedAppointments = appointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price,
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'User appointments retrieved successfully', {
            appointments: formattedAppointments,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Get user appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/user/payment-confirmations - Get all user payment confirmations with pagination
router.get('/payment-confirmations', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const status = req.query.status;
        const query = { user: req.user._id };
        if (status) {
            query.status = status;
        }
        const paymentConfirmations = await models_1.PaymentConfirmation.find(query)
            .populate([
            { path: 'appointment', populate: { path: 'service', select: 'name' } },
            { path: 'order' }
        ])
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit);
        const total = await models_1.PaymentConfirmation.countDocuments(query);
        const formattedConfirmations = paymentConfirmations.map(confirmation => ({
            id: confirmation._id,
            appointmentId: confirmation.appointment?._id,
            appointmentService: confirmation.appointment?.service?.name,
            orderId: confirmation.order?._id,
            amount: confirmation.amount,
            paymentMethod: confirmation.paymentMethod,
            proofImage: confirmation.proofImage,
            notes: confirmation.notes,
            status: confirmation.status,
            verifiedAt: confirmation.verifiedAt,
            rejectionReason: confirmation.rejectionReason,
            createdAt: confirmation.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'User payment confirmations retrieved successfully', {
            paymentConfirmations: formattedConfirmations,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error('Get user payment confirmations error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
