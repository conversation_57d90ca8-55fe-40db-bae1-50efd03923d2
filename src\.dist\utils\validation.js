"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mongoIdValidation = exports.paginationValidation = exports.paymentConfirmationStatusValidation = exports.updatePaymentConfirmationValidation = exports.createPaymentConfirmationValidation = exports.reviewStatusValidation = exports.updateReviewValidation = exports.createReviewValidation = exports.createOrderValidation = exports.updateCartItemValidation = exports.addToCartValidation = exports.updateAppointmentValidation = exports.createAppointmentValidation = exports.forgotPasswordValidation = exports.loginValidation = void 0;
const express_validator_1 = require("express-validator");
// Note: Registration validation removed - signup functionality disabled
exports.loginValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('Password is required')
];
exports.forgotPasswordValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email')
];
// Appointment validation
exports.createAppointmentValidation = [
    (0, express_validator_1.body)('service')
        .isMongoId()
        .withMessage('Please provide a valid service ID'),
    (0, express_validator_1.body)('date')
        .isISO8601()
        .toDate()
        .withMessage('Please provide a valid date'),
    (0, express_validator_1.body)('time')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Please provide a valid time in HH:MM format'),
    (0, express_validator_1.body)('name')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('phone')
        .optional()
        .custom((value) => {
        if (value && value.trim() !== '') {
            // Only validate if phone is provided and not empty
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value)) {
                throw new Error('Please provide a valid phone number');
            }
        }
        return true;
    }),
    (0, express_validator_1.body)('message')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Message cannot exceed 500 characters')
];
exports.updateAppointmentValidation = [
    (0, express_validator_1.param)('id')
        .isMongoId()
        .withMessage('Please provide a valid appointment ID'),
    (0, express_validator_1.body)('date')
        .optional()
        .isISO8601()
        .toDate()
        .withMessage('Please provide a valid date'),
    (0, express_validator_1.body)('time')
        .optional()
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Please provide a valid time in HH:MM format'),
    (0, express_validator_1.body)('status')
        .optional()
        .isIn(['pending', 'confirmed', 'completed', 'cancelled'])
        .withMessage('Invalid status')
];
// Cart validation
exports.addToCartValidation = [
    (0, express_validator_1.body)('productId')
        .isMongoId()
        .withMessage('Please provide a valid product ID'),
    (0, express_validator_1.body)('quantity')
        .isInt({ min: 1, max: 100 })
        .withMessage('Quantity must be between 1 and 100')
];
exports.updateCartItemValidation = [
    (0, express_validator_1.param)('itemId')
        .isMongoId()
        .withMessage('Please provide a valid item ID'),
    (0, express_validator_1.body)('quantity')
        .isInt({ min: 1, max: 100 })
        .withMessage('Quantity must be between 1 and 100')
];
// Order validation
exports.createOrderValidation = [
    (0, express_validator_1.body)('items')
        .isArray({ min: 1 })
        .withMessage('Order must contain at least one item'),
    (0, express_validator_1.body)('items.*.productId')
        .isMongoId()
        .withMessage('Please provide valid product IDs'),
    (0, express_validator_1.body)('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('Quantity must be at least 1'),
    (0, express_validator_1.body)('shippingAddress.street')
        .trim()
        .isLength({ min: 5, max: 200 })
        .withMessage('Street address must be between 5 and 200 characters'),
    (0, express_validator_1.body)('shippingAddress.city')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('City must be between 2 and 50 characters'),
    (0, express_validator_1.body)('shippingAddress.state')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('State must be between 2 and 50 characters'),
    (0, express_validator_1.body)('shippingAddress.zip')
        .matches(/^\d{5}(-\d{4})?$/)
        .withMessage('Please provide a valid ZIP code'),
    (0, express_validator_1.body)('paymentMethod')
        .isIn(['credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery'])
        .withMessage('Invalid payment method')
];
// Review validation
exports.createReviewValidation = [
    (0, express_validator_1.body)('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),
    (0, express_validator_1.body)('title')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Title is required and must be less than 100 characters'),
    (0, express_validator_1.body)('comment')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Comment is required and must be less than 1000 characters'),
    (0, express_validator_1.body)('product')
        .optional()
        .isMongoId()
        .withMessage('Invalid product ID'),
    (0, express_validator_1.body)('service')
        .optional()
        .isMongoId()
        .withMessage('Invalid service ID'),
    (0, express_validator_1.body)()
        .custom((value) => {
        if (!value.product && !value.service) {
            throw new Error('Either product or service must be specified');
        }
        if (value.product && value.service) {
            throw new Error('Cannot review both product and service in the same review');
        }
        return true;
    })
];
exports.updateReviewValidation = [
    (0, express_validator_1.body)('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),
    (0, express_validator_1.body)('title')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Title must be less than 100 characters'),
    (0, express_validator_1.body)('comment')
        .optional()
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Comment must be less than 1000 characters')
];
exports.reviewStatusValidation = [
    (0, express_validator_1.body)('status')
        .isIn(['approved', 'rejected'])
        .withMessage('Status must be either approved or rejected')
];
// Payment confirmation validation
exports.createPaymentConfirmationValidation = [
    (0, express_validator_1.body)('amount')
        .isFloat({ min: 0 })
        .withMessage('Amount must be a positive number'),
    (0, express_validator_1.body)('paymentMethod')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Payment method is required and must be less than 50 characters'),
    (0, express_validator_1.body)('proofImage')
        .notEmpty()
        .withMessage('Payment proof image is required'),
    (0, express_validator_1.body)('order')
        .optional()
        .isMongoId()
        .withMessage('Invalid order ID'),
    (0, express_validator_1.body)('appointment')
        .optional()
        .isMongoId()
        .withMessage('Invalid appointment ID'),
    (0, express_validator_1.body)('notes')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Notes must be less than 500 characters'),
    (0, express_validator_1.body)()
        .custom((value) => {
        if (!value.order && !value.appointment) {
            throw new Error('Either order or appointment must be specified');
        }
        if (value.order && value.appointment) {
            throw new Error('Cannot specify both order and appointment');
        }
        return true;
    })
];
exports.updatePaymentConfirmationValidation = [
    (0, express_validator_1.body)('amount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Amount must be a positive number'),
    (0, express_validator_1.body)('paymentMethod')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Payment method must be less than 50 characters'),
    (0, express_validator_1.body)('notes')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Notes must be less than 500 characters'),
    (0, express_validator_1.body)('proofImage')
        .optional()
        .notEmpty()
        .withMessage('Payment proof image cannot be empty')
];
exports.paymentConfirmationStatusValidation = [
    (0, express_validator_1.body)('status')
        .isIn(['verified', 'rejected'])
        .withMessage('Status must be either verified or rejected'),
    (0, express_validator_1.body)('rejectionReason')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Rejection reason must be less than 200 characters')
];
// Query validation
exports.paginationValidation = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100')
];
const mongoIdValidation = (field = 'id') => [
    (0, express_validator_1.param)(field)
        .isMongoId()
        .withMessage(`Please provide a valid ${field}`)
];
exports.mongoIdValidation = mongoIdValidation;
