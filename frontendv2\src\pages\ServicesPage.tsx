import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import Header from '../components/Layout/Header'
import { serviceAPI, type Service } from '../utils/serviceAPI'
import { type User } from '../utils/api'

interface ServicesPageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function ServicesPage({ currentUser, onLogout }: ServicesPageProps) {
  const navigate = useNavigate();
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  useEffect(() => {
    fetchServices();
    fetchCategories();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceAPI.getServices({
        isActive: true, // Only show active services to customers
        sortBy: 'category',
        sortOrder: 'asc'
      });
      
      if (response.success) {
        setServices(response.data.services);
      } else {
        throw new Error('Failed to load services');
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      setError('Failed to load services. Please try again.');
      setServices([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await serviceAPI.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };





  // Filter services based on category and search term
  const filteredServices = services.filter(service => {
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // Group services by category for display
  const groupedServices = filteredServices.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {} as Record<string, Service[]>);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const formatDuration = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} min`;
    } else if (hours === 1) {
      return '1 hour';
    } else if (hours % 1 === 0) {
      return `${hours} hours`;
    } else {
      const wholeHours = Math.floor(hours);
      const minutes = Math.round((hours - wholeHours) * 60);
      return `${wholeHours}h ${minutes}m`;
    }
  };

  const handleBookService = (service: Service) => {
    // Store selected service in sessionStorage for booking flow
    sessionStorage.setItem('selectedService', JSON.stringify(service));
    navigate('/booking/datetime');
  };

  if (loading) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="loading-container">
            <p>Loading services...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app">
        <Header currentUser={currentUser} onLogout={onLogout} />
        <main className="main-content">
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button
              className="btn-primary"
              onClick={() => {
                setError(null);
                fetchServices();
              }}
            >
              Try Again
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={onLogout} />
      
      <main className="main-content">
        <div className="page-header">
          <h1>Our Services</h1>
          <p>Choose from our range of professional services</p>
        </div>

        {/* Search and Filter */}
        <div className="services-filters">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="category-filters">
            <button
              className={`filter-btn ${selectedCategory === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('all')}
            >
              All Services
            </button>
            {categories.map(category => (
              <button
                key={category}
                className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Services Display */}
        {Object.keys(groupedServices).length === 0 ? (
          <div className="empty-state">
            <h3>No services found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        ) : (
          <div className="services-content">
            {Object.entries(groupedServices).map(([category, categoryServices]) => (
              <div key={category} className="service-category-section">
                <h2 className="category-title">{category}</h2>
                <div className="services-grid">
                  {categoryServices.map(service => (
                    <div key={service.id} className="service-card">
                      <div className="service-header">
                        <h3>{service.name}</h3>
                        <div className="service-price">{formatPrice(service.price)}</div>
                      </div>
                      
                      <div className="service-description">
                        {service.description}
                      </div>
                      
                      <div className="service-details">
                        <span className="service-duration">
                          Duration: {formatDuration(service.duration)}
                        </span>
                      </div>



                      <div className="service-actions">
                        <button
                          onClick={() => handleBookService(service)}
                          className="btn-primary"
                        >
                          Book Now
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
