import { useState, useEffect, useCallback } from 'react';
import { adminAPI } from '../../utils/api';
import LoadingSpinner from '../LoadingSpinner';
import ReviewModal from './ReviewModal';
import { useNotifications } from '../../hooks/useRealTimeUpdates';
import { useToast } from '../../contexts/ToastContext';

interface Review {
  _id: string;
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  service?: {
    _id: string;
    name: string;
  };
  appointment?: {
    _id: string;
    date: string;
    time: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  adminResponse?: string;
  adminResponseDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface ReviewFilters {
  page: number;
  limit: number;
  status: string;
  service?: string;
  rating?: number;
  sortBy: string;
  sortOrder: string;
}

interface ReviewManagementProps {
  onError?: (message: string) => void;
  onSuccess?: (message: string) => void;
}

export default function ReviewManagement({ onError, onSuccess }: ReviewManagementProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  
  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [modalType, setModalType] = useState<'view' | 'edit' | 'respond'>('view');
  
  // Filter states
  const [filters, setFilters] = useState<ReviewFilters>({
    page: 1,
    limit: 20,
    status: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  
  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Real-time notifications
  const { showNotification } = useNotifications();
  const { showWarning } = useToast();

  // Load reviews
  const loadReviews = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await adminAPI.getReviews(filters);
      
      if (response.success) {
        setReviews(response.data.reviews);
        setPagination(response.data.pagination);
      } else {
        setError('Failed to load reviews');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load reviews');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadReviews();
  }, [loadReviews]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<ReviewFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  // Handle review status update
  const handleStatusUpdate = async (reviewId: string, status: string, adminNote?: string) => {
    try {
      const response = await adminAPI.updateReviewStatus(reviewId, status, adminNote);

      if (response.success) {
        // Update the review in the list
        setReviews(prev =>
          prev.map(review =>
            review._id === reviewId
              ? { 
                  ...review, 
                  status: status as any, 
                  adminResponse: adminNote || review.adminResponse,
                  adminResponseDate: adminNote ? new Date().toISOString() : review.adminResponseDate,
                  updatedAt: new Date().toISOString() 
                }
              : review
          )
        );

        // Show success notification
        showNotification(`Review ${status} successfully`, 'success');
        onSuccess?.(`Review ${status} successfully`);
      } else {
        setError('Failed to update review status');
        onError?.('Failed to update review status');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update review status';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedReviews.length === 0) return;

    try {
      // Update each selected review
      const promises = selectedReviews.map(reviewId => 
        adminAPI.updateReviewStatus(reviewId, status)
      );

      const results = await Promise.all(promises);
      const successCount = results.filter(r => r.success).length;

      if (successCount > 0) {
        // Reload reviews to get updated data
        await loadReviews();
        setSelectedReviews([]);
        showNotification(`${successCount} reviews updated to ${status}`, 'success');
        onSuccess?.(`${successCount} reviews updated to ${status}`);
      } else {
        setError('Failed to update reviews');
        onError?.('Failed to update reviews');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update reviews';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  // Handle review deletion
  const handleDelete = async (reviewId: string) => {
    // Show warning and get confirmation
    showWarning('Are you sure you want to delete this review? This action cannot be undone.', 5000);

    // For now, we'll proceed with deletion. In a real app, you'd want a proper confirmation dialog
    const confirmed = window.confirm('Are you sure you want to delete this review? This action cannot be undone.');
    if (!confirmed) {
      return;
    }

    try {
      const response = await adminAPI.deleteReview(reviewId);

      if (response.success) {
        setReviews(prev => prev.filter(review => review._id !== reviewId));
        showNotification('Review deleted successfully', 'success');
        onSuccess?.('Review deleted successfully');
      } else {
        setError('Failed to delete review');
        onError?.('Failed to delete review');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete review';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  // Handle modal actions
  const handleView = (review: Review) => {
    setEditingReview(review);
    setModalType('view');
    setShowModal(true);
  };

  const handleEdit = (review: Review) => {
    setEditingReview(review);
    setModalType('edit');
    setShowModal(true);
  };

  const handleRespond = (review: Review) => {
    setEditingReview(review);
    setModalType('respond');
    setShowModal(true);
  };

  // Handle review save
  const handleSave = async (reviewData: any) => {
    try {
      let response;

      if (modalType === 'edit' && editingReview) {
        response = await adminAPI.updateReview(editingReview._id, reviewData);
      } else if (modalType === 'respond' && editingReview) {
        response = await adminAPI.updateReviewStatus(
          editingReview._id, 
          editingReview.status, 
          reviewData.adminResponse
        );
      }

      if (response?.success) {
        await loadReviews();
        setShowModal(false);
        setEditingReview(null);
        showNotification(
          `Review ${modalType === 'edit' ? 'updated' : 'response added'} successfully`,
          'success'
        );
        onSuccess?.(`Review ${modalType === 'edit' ? 'updated' : 'response added'} successfully`);
      } else {
        const errorMessage = `Failed to ${modalType === 'edit' ? 'update' : 'respond to'} review`;
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${modalType === 'edit' ? 'update' : 'respond to'} review`;
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  // Render stars
  const renderStars = (rating: number) => {
    return (
      <div className="rating-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${star <= rating ? 'filled' : 'empty'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'status-badge approved';
      case 'rejected':
        return 'status-badge rejected';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  return (
    <div className="review-management">
      <div className="review-management-header">
        <div className="header-content">
          <h2>Review Management</h2>
          <p>Manage customer reviews and feedback</p>
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)} className="error-close">×</button>
        </div>
      )}

      {/* Filters */}
      <div className="appointment-filters">
        <div className="filters-row">
          <div className="filter-group">
            <label>Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange({ status: e.target.value })}
              className="filter-select"
            >
              <option value="all">All Reviews</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Rating</label>
            <select
              value={filters.rating || ''}
              onChange={(e) => handleFilterChange({ rating: e.target.value ? parseInt(e.target.value) : undefined })}
              className="filter-select"
            >
              <option value="">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Sort By</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange({ sortBy: e.target.value })}
              className="filter-select"
            >
              <option value="createdAt">Date Created</option>
              <option value="rating">Rating</option>
              <option value="status">Status</option>
            </select>
          </div>

          <div className="filter-group">
            <label>Order</label>
            <select
              value={filters.sortOrder}
              onChange={(e) => handleFilterChange({ sortOrder: e.target.value })}
              className="filter-select"
            >
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedReviews.length > 0 && (
        <div className="bulk-actions">
          <div className="selected-count">
            {selectedReviews.length} review{selectedReviews.length !== 1 ? 's' : ''} selected
          </div>
          <div className="bulk-actions-buttons">
            <button
              onClick={() => handleBulkStatusUpdate('approved')}
              className="btn btn-sm btn-primary"
            >
              Approve Selected
            </button>
            <button
              onClick={() => handleBulkStatusUpdate('rejected')}
              className="btn btn-sm btn-danger"
            >
              Reject Selected
            </button>
          </div>
        </div>
      )}

      {loading ? (
        <LoadingSpinner message="Loading reviews..." />
      ) : (
        <>
          {reviews.length > 0 ? (
            <div className="reviews-list">
              {reviews.map((review) => (
                <div key={review._id} className="review-card">
                  <div className="review-header">
                    <div className="review-selection">
                      <input
                        type="checkbox"
                        checked={selectedReviews.includes(review._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedReviews(prev => [...prev, review._id]);
                          } else {
                            setSelectedReviews(prev => prev.filter(id => id !== review._id));
                          }
                        }}
                      />
                    </div>
                    <div className="review-info">
                      <div className="customer-info">
                        <h4>
                          {review.customerName ||
                           (review.user ? `${review.user.firstName} ${review.user.lastName}` : 'Anonymous')}
                        </h4>
                        {review.isVerifiedPurchase && (
                          <span className="verified-badge">✓ Verified</span>
                        )}
                      </div>
                      <div className="review-meta">
                        {renderStars(review.rating)}
                        <span className={getStatusBadgeClass(review.status)}>
                          {review.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="review-actions">
                      <button
                        onClick={() => handleView(review)}
                        className="btn btn-sm btn-outline"
                        title="View Details"
                      >
                        👁️
                      </button>
                      <button
                        onClick={() => handleEdit(review)}
                        className="btn btn-sm btn-outline"
                        title="Edit Review"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleRespond(review)}
                        className="btn btn-sm btn-outline"
                        title="Add Response"
                      >
                        💬
                      </button>
                      {review.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(review._id, 'approved')}
                            className="btn btn-sm btn-primary"
                            title="Approve"
                          >
                            ✓
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(review._id, 'rejected')}
                            className="btn btn-sm btn-danger"
                            title="Reject"
                          >
                            ✗
                          </button>
                        </>
                      )}
                      <button
                        onClick={() => handleDelete(review._id)}
                        className="btn btn-sm btn-danger"
                        title="Delete"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  <div className="review-content">
                    <div className="service-info">
                      <strong>Service:</strong> {review.service?.name || 'Unknown Service'}
                      {review.appointment && (
                        <span className="appointment-info">
                          • Appointment: {new Date(review.appointment.date).toLocaleDateString()} at {review.appointment.time}
                        </span>
                      )}
                    </div>

                    {review.title && (
                      <h5 className="review-title">{review.title}</h5>
                    )}

                    {review.comment && (
                      <p className="review-comment">{review.comment}</p>
                    )}

                    {review.adminResponse && (
                      <div className="admin-response">
                        <div className="admin-response-label">Business Response:</div>
                        <div className="admin-response-text">{review.adminResponse}</div>
                        {review.adminResponseDate && (
                          <div className="admin-response-date">
                            {new Date(review.adminResponseDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    )}

                    <div className="review-footer">
                      <span className="review-date">
                        Created: {new Date(review.createdAt).toLocaleDateString()}
                      </span>
                      {review.helpfulVotes > 0 && (
                        <span className="helpful-votes">
                          👍 {review.helpfulVotes} helpful
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-state-icon">⭐</div>
              <h3 className="empty-state-title">No Reviews Found</h3>
              <p className="empty-state-subtitle">
                {filters.status !== 'all' || filters.rating
                  ? 'Try adjusting your filter criteria.'
                  : 'No reviews have been submitted yet.'
                }
              </p>
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="pagination">
              <button
                onClick={() => handleFilterChange({ page: pagination.page - 1 })}
                disabled={pagination.page <= 1}
                className="btn btn-outline"
              >
                Previous
              </button>

              <span className="pagination-info">
                Page {pagination.page} of {pagination.pages} ({pagination.total} total)
              </span>

              <button
                onClick={() => handleFilterChange({ page: pagination.page + 1 })}
                disabled={pagination.page >= pagination.pages}
                className="btn btn-outline"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}

      {/* Modal */}
      {showModal && (
        <ReviewModal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingReview(null);
          }}
          onSave={handleSave}
          review={editingReview}
          mode={modalType}
        />
      )}
    </div>
  );
}
