# API Configuration Guide

This document explains how the frontend v2 application handles API configuration and environment variables.

## Environment Variables

The application uses environment variables to configure API endpoints and other settings. Create a `.env` file in the root directory based on `.env.example`:

```bash
cp .env.example .env
```

### Required Environment Variables

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v2

# App Configuration
VITE_APP_TITLE=MicroLocs - Service Management
VITE_APP_DESCRIPTION=Professional service management and booking platform
VITE_APP_URL=http://localhost:5174

# Development Configuration
VITE_NODE_ENV=development
```

### Environment-Specific URLs

The application automatically selects the appropriate API URL based on the environment:

- **Development**: `http://localhost:3001/api/v2`
- **Production**: `https://microlocsbackend-xgeg.onrender.com/api/v2`
- **Custom**: Set `VITE_API_BASE_URL` to override

## API Configuration

All API calls use the centralized configuration from `src/utils/config.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL ||
    (import.meta.env.MODE === 'production'
      ? 'https://microlocsbackend-xgeg.onrender.com/api/v2'
      : 'http://localhost:3001/api/v2'),
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
};
```

## Updated Components

The following components have been updated to use configurable URLs:

### 1. Checkout Component (`src/components/Checkout.tsx`)
- **Before**: `http://localhost:3000/api/upload/payment-proof`
- **After**: `${API_CONFIG.BASE_URL.replace('/v2', '')}/upload/payment-proof`

### 2. BookingDetails Component (`src/components/BookingDetails.tsx`)
- **Before**: `http://localhost:3000/api/v2/auth/check-email`
- **After**: `${API_CONFIG.BASE_URL}/auth/check-email`

## Removed Client Restrictions

The following client restriction text has been removed:

- **BookingDetails Component**: Removed "*EXISTING CLIENTS ONLY*" text from appointment summary

## API Utilities

All API utility files already use the configurable base URL:

- `src/utils/api.ts` - Main API functions
- `src/utils/appointmentAPI.ts` - Appointment-specific APIs
- `src/utils/serviceAPI.ts` - Service management APIs
- `src/utils/customerAPI.ts` - Customer management APIs
- `src/utils/settingsAPI.ts` - Settings APIs

## Deployment Configuration

### Development
```bash
npm run dev
# Uses http://localhost:3001/api/v2 by default
```

### Production
```bash
npm run build
# Uses https://microlocsbackend-xgeg.onrender.com/api/v2 by default
```

### Custom Environment
```bash
VITE_API_BASE_URL=https://your-api-domain.com/api/v2 npm run build
```

## Testing Configuration

To test with different API endpoints:

1. Update your `.env` file:
   ```env
   VITE_API_BASE_URL=https://staging-api.example.com/api/v2
   ```

2. Restart the development server:
   ```bash
   npm run dev
   ```

## Troubleshooting

### Common Issues

1. **API calls failing**: Check that `VITE_API_BASE_URL` is correctly set
2. **CORS errors**: Ensure the backend allows requests from your frontend domain
3. **404 errors**: Verify the API endpoint paths match your backend routes

### Debug Mode

In development, the configuration is logged to the console:

```javascript
console.log('🔧 API Configuration:', {
  BASE_URL: API_CONFIG.BASE_URL,
  MODE: import.meta.env.MODE,
  NODE_ENV: APP_CONFIG.NODE_ENV
});
```

## Security Notes

- Never commit `.env` files containing production secrets
- Use environment-specific configuration files for different deployments
- Ensure API endpoints use HTTPS in production
